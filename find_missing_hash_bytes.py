#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找缺失的哈希字节
分析hash key的完整构成
"""

import struct
import hashlib
import sys

def find_missing_hash_bytes(filename):
    """查找hash key缺失的后16字节"""
    print(f"=== 查找hash key缺失字节 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 完整的hash key
    full_hash_key = bytes.fromhex("2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e")
    print(f"完整hash key: {full_hash_key.hex()}")
    
    # 前16字节 (已找到在cert+0x12c)
    first_16_bytes = full_hash_key[:16]
    print(f"前16字节: {first_16_bytes.hex()}")
    
    # 后16字节 (需要查找)
    last_16_bytes = full_hash_key[16:]
    print(f"后16字节: {last_16_bytes.hex()}")
    
    # 验证cert+0x12c确实包含前16字节
    cert_0x12c = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    print(f"cert+0x12c: {cert_0x12c.hex()}")
    print(f"前16字节匹配: {'YES' if cert_0x12c == first_16_bytes else 'NO'}")
    
    # 查找后16字节在文件中的位置
    print(f"\n=== 搜索后16字节的位置 ===")
    
    # 检查cert+0x13c (紧接着0x12c)
    cert_0x13c = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
    print(f"cert+0x13c: {cert_0x13c.hex()}")
    print(f"后16字节匹配: {'YES' if cert_0x13c == last_16_bytes else 'NO'}")
    
    if cert_0x13c == last_16_bytes:
        print(f"🎉 找到了！后16字节在cert+0x13c位置")
        print(f"完整hash key = cert+0x12c (16字节) + cert+0x13c (16字节)")
        return True
    
    # 如果不在0x13c，搜索整个证书区域
    print(f"\n在证书区域搜索后16字节...")
    cert_size = 0x200  # 假设证书大小
    for offset in range(0, cert_size, 4):
        pos = cert_offset + offset
        if pos + 16 <= len(data):
            chunk = data[pos:pos + 16]
            if chunk == last_16_bytes:
                print(f"找到后16字节在: cert+0x{offset:x} (文件偏移0x{pos:x})")
                return True
    
    # 搜索整个文件
    print(f"\n在整个文件中搜索后16字节...")
    for i in range(len(data) - 15):
        if data[i:i + 16] == last_16_bytes:
            print(f"找到后16字节在文件偏移: 0x{i:x}")
            # 计算相对于证书的偏移
            rel_offset = i - cert_offset
            print(f"相对证书偏移: cert+0x{rel_offset:x}")
    
    return False

def analyze_hash_key_construction(filename):
    """分析hash key的构造方式"""
    print(f"\n=== 分析hash key构造方式 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 检查证书的关键位置
    positions_to_check = [
        (0x10c, "数据哈希位置"),
        (0x12c, "密钥哈希位置1"),
        (0x13c, "密钥哈希位置2"),
        (0x14c, "签名开始位置"),
    ]
    
    print(f"证书关键位置分析:")
    for offset, description in positions_to_check:
        pos = cert_offset + offset
        if pos + 32 <= len(data):
            chunk = data[pos:pos + 32]
            print(f"  cert+0x{offset:x} ({description}): {chunk.hex()}")
    
    # 尝试组合不同位置的数据
    print(f"\n尝试组合构造hash key:")
    
    # 方案1: cert+0x12c + cert+0x13c
    part1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    part2 = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
    combined1 = part1 + part2
    
    target_hash = bytes.fromhex("2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e")
    
    print(f"方案1 (cert+0x12c + cert+0x13c): {combined1.hex()}")
    print(f"目标hash key:                   {target_hash.hex()}")
    print(f"匹配: {'YES' if combined1 == target_hash else 'NO'}")
    
    if combined1 == target_hash:
        print(f"🎉 找到正确的构造方式！")
        print(f"hash key = cert+0x12c (16字节) + cert+0x13c (16字节)")
        return True
    
    return False

def verify_hash_calculation_with_combined_key(filename):
    """使用组合的密钥验证哈希计算"""
    print(f"\n=== 使用组合密钥验证哈希计算 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 获取组合的hash key
    part1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    part2 = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
    combined_hash_key = part1 + part2
    
    print(f"组合的hash key: {combined_hash_key.hex()}")
    
    # 现在尝试找到能产生这个hash key的数据
    print(f"尝试找到产生此hash key的源数据...")
    
    # 基于汇编分析，应该是SHA256(cert + 4, 264)
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_hash = hashlib.sha256(key_data).digest()
    
    print(f"SHA256(cert+4, 264): {calculated_hash.hex()}")
    print(f"组合hash key:        {combined_hash_key.hex()}")
    print(f"匹配: {'YES' if calculated_hash == combined_hash_key else 'NO'}")
    
    # 如果不匹配，尝试其他可能的数据范围
    if calculated_hash != combined_hash_key:
        print(f"\n尝试其他数据范围...")
        
        test_ranges = [
            (0, 264, "cert+0, 264字节"),
            (4, 260, "cert+4, 260字节"),
            (8, 256, "cert+8, 256字节"),
            (12, 252, "cert+12, 252字节"),
            (0, 268, "cert+0, 268字节"),
            (4, 268, "cert+4, 268字节"),
        ]
        
        for start_offset, length, description in test_ranges:
            test_data = data[cert_offset + start_offset:cert_offset + start_offset + length]
            test_hash = hashlib.sha256(test_data).digest()
            match = test_hash == combined_hash_key
            print(f"  {description}: {'MATCH!' if match else 'NO'}")
            
            if match:
                print(f"🎉 找到正确的计算方法: {description}")
                return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python find_missing_hash_bytes.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("查找缺失的hash key字节")
    print("="*50)
    
    # 1. 查找缺失的字节位置
    found_bytes = find_missing_hash_bytes(filename)
    
    # 2. 分析hash key构造方式
    construction_found = analyze_hash_key_construction(filename)
    
    # 3. 验证哈希计算
    if construction_found:
        calculation_verified = verify_hash_calculation_with_combined_key(filename)
    else:
        calculation_verified = False
    
    print(f"\n{'='*50}")
    print(f"缺失字节查找: {'✅ 成功' if found_bytes else '❌ 失败'}")
    print(f"构造方式分析: {'✅ 成功' if construction_found else '❌ 失败'}")
    print(f"哈希计算验证: {'✅ 成功' if calculation_verified else '❌ 失败'}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
