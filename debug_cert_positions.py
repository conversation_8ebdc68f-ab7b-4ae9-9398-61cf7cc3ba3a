#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试证书位置脚本
仔细检查各个位置存储的内容
"""

import struct
import hashlib
import sys

def debug_cert_positions(filename):
    """调试证书各个位置的内容"""
    print(f"=== 调试证书位置 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 计算密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    print(f"\n计算的密钥哈希: {calculated_key_hash.hex()}")
    
    # 检查各个可能的存储位置
    positions_to_check = [
        (0x10c, 32, "cert+0x10c (268) - payload哈希位置"),
        (0x12c, 32, "cert+0x12c (300) - 可能的密钥哈希位置1"),
        (0x13c, 32, "cert+0x13c (316) - 可能的密钥哈希位置2"),
        (0x14c, 32, "cert+0x14c (332) - 可能的密钥哈希位置3"),
        (0x15c, 32, "cert+0x15c (348) - 可能的密钥哈希位置4"),
    ]
    
    print(f"\n=== 检查各个位置的内容 ===")
    for offset, length, description in positions_to_check:
        pos = cert_offset + offset
        if pos + length <= len(data):
            stored_data = data[pos:pos + length]
            print(f"{description}:")
            print(f"  存储值: {stored_data.hex()}")
            
            # 检查是否匹配计算的密钥哈希
            if stored_data == calculated_key_hash:
                print(f"  ✅ 匹配计算的密钥哈希！")
            else:
                print(f"  ❌ 不匹配计算的密钥哈希")
            
            # 检查前16字节是否匹配
            if stored_data[:16] == calculated_key_hash[:16]:
                print(f"  ✅ 前16字节匹配！")
            
            # 检查后16字节是否匹配
            if stored_data[16:] == calculated_key_hash[16:]:
                print(f"  ✅ 后16字节匹配！")
            
            print()
    
    # 搜索完整的计算哈希在文件中的位置
    print(f"=== 搜索计算哈希在文件中的位置 ===")
    found_positions = []
    for i in range(len(data) - 31):
        if data[i:i + 32] == calculated_key_hash:
            found_positions.append(i)
    
    if found_positions:
        print(f"找到完整计算哈希的位置:")
        for pos in found_positions:
            print(f"  偏移: 0x{pos:x} (相对cert: +0x{pos - cert_offset:x})")
    else:
        print(f"未找到完整的计算哈希")
    
    # 搜索前16字节
    print(f"\n=== 搜索计算哈希前16字节的位置 ===")
    front_16 = calculated_key_hash[:16]
    found_front = []
    for i in range(len(data) - 15):
        if data[i:i + 16] == front_16:
            found_front.append(i)
    
    if found_front:
        print(f"找到前16字节的位置:")
        for pos in found_front:
            print(f"  偏移: 0x{pos:x} (相对cert: +0x{pos - cert_offset:x})")
    
    # 搜索后16字节
    print(f"\n=== 搜索计算哈希后16字节的位置 ===")
    back_16 = calculated_key_hash[16:]
    found_back = []
    for i in range(len(data) - 15):
        if data[i:i + 16] == back_16:
            found_back.append(i)
    
    if found_back:
        print(f"找到后16字节的位置:")
        for pos in found_back:
            print(f"  偏移: 0x{pos:x} (相对cert: +0x{pos - cert_offset:x})")

def main():
    if len(sys.argv) != 2:
        print("用法: python debug_cert_positions.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    debug_cert_positions(filename)
    return 0

if __name__ == "__main__":
    sys.exit(main())
