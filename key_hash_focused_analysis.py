#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专注于密钥哈希计算的分析工具
基于用户已解决payload SHA256的基础上
"""

import struct
import hashlib
import sys

def analyze_key_hash_calculation(filename):
    """专注分析密钥哈希计算"""
    print(f"=== 密钥哈希计算专项分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基本信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"证书类型: {cert_type}")
    print(f"密钥位长度: 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"e值: 0x{e_value:x}")
    
    # 读取目标哈希值
    if cert_type == 1:
        target_key_hash = data[cert_offset + 300:cert_offset + 300 + 32]
        print(f"目标密钥哈希: {target_key_hash.hex()}")
    else:
        print("Type 0证书，无密钥哈希字段")
        return False
    
    # 根据RSA验证函数的汇编代码分析
    print(f"\n=== RSA验证函数中的密钥哈希计算 ===")
    print("汇编代码分析:")
    print("0x9f009e80: ADD X0, X19, #4        ; cert_address + 4")
    print("0x9f009e8c: MOV W1, #0x108         ; 264字节")
    print("0x9f009e94: BL sha256_hash_wrapper  ; 计算SHA256")
    
    # 标准计算方法
    key_data_start = cert_offset + 4
    key_data_length = 0x108  # 264字节
    key_data = data[key_data_start:key_data_start + key_data_length]
    standard_hash = hashlib.sha256(key_data).digest()
    
    print(f"\n标准计算方法:")
    print(f"数据范围: 0x{key_data_start:x} - 0x{key_data_start + key_data_length:x}")
    print(f"数据长度: {key_data_length} bytes")
    print(f"计算结果: {standard_hash.hex()}")
    print(f"匹配: {'YES' if standard_hash == target_key_hash else 'NO'}")
    
    if standard_hash == target_key_hash:
        print("🎉 标准方法匹配！")
        return True
    
    # 详细分析密钥数据结构
    print(f"\n=== 密钥数据结构详细分析 ===")
    print(f"密钥数据前32字节: {key_data[:32].hex()}")
    print(f"密钥数据后32字节: {key_data[-32:].hex()}")
    
    # 分解密钥数据
    print(f"\n密钥数据分解:")
    print(f"位长度字段 (4字节): {key_data[0:4].hex()}")
    print(f"e值字段 (4字节): {key_data[4:8].hex()}")
    print(f"模数字段 (256字节): {key_data[8:264].hex()[:32]}...{key_data[8:264].hex()[-32:]}")
    
    # 尝试不同的组合方式
    print(f"\n=== 尝试不同的密钥哈希计算方式 ===")
    
    # 方法1: 不包含位长度字段
    method1_data = key_data[4:]  # 跳过前4字节
    method1_hash = hashlib.sha256(method1_data).digest()
    print(f"方法1 (跳过位长度): {method1_hash.hex()[:32]}... 匹配: {'YES' if method1_hash == target_key_hash else 'NO'}")
    
    # 方法2: 只包含e值和模数
    method2_data = key_data[4:8] + key_data[8:264]  # e值 + 模数
    method2_hash = hashlib.sha256(method2_data).digest()
    print(f"方法2 (e值+模数): {method2_hash.hex()[:32]}... 匹配: {'YES' if method2_hash == target_key_hash else 'NO'}")
    
    # 方法3: 只包含模数
    method3_data = key_data[8:264]  # 只有模数
    method3_hash = hashlib.sha256(method3_data).digest()
    print(f"方法3 (仅模数): {method3_hash.hex()[:32]}... 匹配: {'YES' if method3_hash == target_key_hash else 'NO'}")
    
    # 方法4: 包含额外数据
    if cert_offset + 4 + 268 <= len(data):
        method4_data = data[cert_offset + 4:cert_offset + 4 + 268]  # 264 + 4 = 268字节
        method4_hash = hashlib.sha256(method4_data).digest()
        print(f"方法4 (+4字节): {method4_hash.hex()[:32]}... 匹配: {'YES' if method4_hash == target_key_hash else 'NO'}")
    
    # 方法5: 字节序转换
    method5_data = bytearray(key_data)
    # 转换位长度和e值的字节序
    method5_data[0:4] = struct.pack('>I', key_bit_len)
    method5_data[4:8] = struct.pack('>I', e_value)
    method5_hash = hashlib.sha256(method5_data).digest()
    print(f"方法5 (大端序): {method5_hash.hex()[:32]}... 匹配: {'YES' if method5_hash == target_key_hash else 'NO'}")
    
    # 检查是否有任何方法匹配
    methods = [method1_hash, method2_hash, method3_hash, method4_hash, method5_hash]
    for i, method_hash in enumerate(methods, 1):
        if method_hash == target_key_hash:
            print(f"🎉 方法{i}匹配！找到正确的密钥哈希计算方式")
            return True
    
    return False

def analyze_certificate_layout(filename):
    """分析证书的详细布局"""
    print(f"\n=== 证书布局详细分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书起始位置: 0x{cert_offset:x}")
    
    # 显示证书的前几个关键区域
    offsets_to_check = [
        (0, "证书类型"),
        (4, "密钥位长度"),
        (8, "e值"),
        (12, "模数开始"),
        (268, "数据哈希"),
        (300, "密钥哈希"),
        (332, "可能的签名开始"),
    ]
    
    for offset, description in offsets_to_check:
        pos = cert_offset + offset
        if pos + 32 <= len(data):
            chunk = data[pos:pos + 32]
            print(f"偏移+{offset:3d} ({description:12s}): {chunk.hex()}")
    
    # 寻找可能的密钥哈希计算边界
    print(f"\n=== 寻找密钥哈希计算边界 ===")
    
    target_hash = data[cert_offset + 300:cert_offset + 300 + 32]
    
    # 测试从cert+4开始的不同长度
    for length in range(256, 280, 4):
        if cert_offset + 4 + length <= len(data):
            test_data = data[cert_offset + 4:cert_offset + 4 + length]
            test_hash = hashlib.sha256(test_data).digest()
            if test_hash == target_hash:
                print(f"🎉 找到匹配！长度: {length} 字节")
                print(f"数据范围: cert+4 到 cert+{4+length}")
                return True
    
    # 测试从不同起始位置
    for start_offset in range(0, 20, 4):
        for length in range(256, 280, 4):
            start_pos = cert_offset + start_offset
            if start_pos + length <= len(data):
                test_data = data[start_pos:start_pos + length]
                test_hash = hashlib.sha256(test_data).digest()
                if test_hash == target_hash:
                    print(f"🎉 找到匹配！起始偏移: +{start_offset}, 长度: {length}")
                    return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python key_hash_focused_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("密钥哈希计算专项分析")
    print("="*50)
    
    # 分析密钥哈希计算
    success1 = analyze_key_hash_calculation(filename)
    
    # 分析证书布局
    success2 = analyze_certificate_layout(filename)
    
    if success1 or success2:
        print(f"\n✅ 找到正确的密钥哈希计算方法！")
    else:
        print(f"\n❌ 仍未找到正确的密钥哈希计算方法")
        print("可能需要更深入的分析或存在特殊的预处理步骤")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
