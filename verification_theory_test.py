#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证理论测试工具
测试新发现的验证逻辑：payload哈希 vs 密钥哈希
"""

import struct
import hashlib
import sys

def test_verification_theory(filename):
    """测试新的验证理论"""
    print(f"=== 验证理论测试: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 1. 计算正确的payload哈希 (基于hash_verifier.py的方法)
    payload_size = struct.unpack('<I', data[48:52])[0]
    hash_start = 0x200
    hash_length = payload_size
    
    payload_hash = hashlib.sha256(data[hash_start:hash_start + hash_length]).digest()
    print(f"Payload哈希: {payload_hash.hex()}")
    
    # 2. 计算密钥哈希 (基于RSA验证函数的逻辑)
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    key_data_start = cert_offset + 4
    key_data_length = 0x108  # 264字节
    key_data = data[key_data_start:key_data_start + key_data_length]
    key_hash = hashlib.sha256(key_data).digest()
    
    print(f"密钥哈希:   {key_hash.hex()}")
    
    # 3. 比较两个哈希
    match = payload_hash == key_hash
    print(f"哈希匹配:   {'YES' if match else 'NO'}")
    
    if match:
        print("🎉 理论验证成功！payload哈希与密钥哈希匹配")
        return True
    
    # 4. 如果不匹配，尝试其他可能的密钥哈希计算方法
    print(f"\n=== 尝试其他密钥哈希计算方法 ===")
    
    methods = [
        ("方法1: 跳过位长度", key_data[4:]),
        ("方法2: e值+模数", key_data[4:8] + key_data[8:264]),
        ("方法3: 仅模数", key_data[8:264]),
        ("方法4: +4字节", data[cert_offset + 4:cert_offset + 4 + 268]),
        ("方法5: 不同起始位置", data[cert_offset:cert_offset + 264]),
    ]
    
    for method_name, method_data in methods:
        if len(method_data) > 0:
            method_hash = hashlib.sha256(method_data).digest()
            method_match = payload_hash == method_hash
            print(f"{method_name}: {'MATCH' if method_match else 'NO'}")
            if method_match:
                print(f"🎉 找到匹配的方法: {method_name}")
                return True
    
    # 5. 检查证书中的其他哈希字段
    print(f"\n=== 检查证书中的哈希字段 ===")
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    data_hash_cert = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"证书数据哈希: {data_hash_cert.hex()}")
    print(f"与payload匹配: {'YES' if payload_hash == data_hash_cert else 'NO'}")
    
    if cert_type == 1:
        hash_300 = data[cert_offset + 300:cert_offset + 300 + 32]
        print(f"证书+300哈希: {hash_300.hex()}")
        print(f"与payload匹配: {'YES' if payload_hash == hash_300 else 'NO'}")
        print(f"与密钥匹配:   {'YES' if key_hash == hash_300 else 'NO'}")
    
    return False

def analyze_verification_flow(filename):
    """分析完整的验证流程"""
    print(f"\n=== 完整验证流程分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print("基于汇编代码分析的验证流程:")
    print()
    
    # 步骤1: 位置检查
    position_check_pos = payload_size + 512
    position_byte = data[position_check_pos] if position_check_pos < len(data) else 0xFF
    print(f"1. 位置检查 (offset {position_check_pos}): 字节值=0x{position_byte:02x} {'PASS' if position_byte == 0 else 'FAIL'}")
    
    # 步骤2: payload哈希计算
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    print(f"2. Payload哈希计算: {payload_hash.hex()}")
    
    # 步骤3: RSA验证函数调用
    print(f"3. RSA验证函数参数:")
    print(f"   - X0 (payload哈希): {payload_hash.hex()}")
    
    # 步骤4: 数据哈希比较
    data_hash_cert = data[cert_offset + 268:cert_offset + 268 + 32]
    data_hash_match = payload_hash == data_hash_cert
    print(f"   - 数据哈希比较: {'PASS' if data_hash_match else 'FAIL'}")
    
    # 步骤5: 密钥哈希计算和比较
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    key_hash = hashlib.sha256(key_data).digest()
    key_hash_match = payload_hash == key_hash
    print(f"   - 密钥哈希比较: {'PASS' if key_hash_match else 'FAIL'}")
    
    # 总结
    print(f"\n验证结果总结:")
    print(f"- 位置检查: {'PASS' if position_byte == 0 else 'FAIL'}")
    print(f"- 数据哈希: {'PASS' if data_hash_match else 'FAIL'}")
    print(f"- 密钥哈希: {'PASS' if key_hash_match else 'FAIL'}")
    
    overall_pass = (position_byte == 0) and data_hash_match and key_hash_match
    print(f"- 整体验证: {'PASS' if overall_pass else 'FAIL'}")
    
    if not overall_pass:
        print(f"\n🔥 发现验证失败点，可能的绕过机会！")
    
    return overall_pass

def main():
    if len(sys.argv) != 2:
        print("用法: python verification_theory_test.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("验证理论测试工具")
    print("="*50)
    
    # 测试验证理论
    theory_correct = test_verification_theory(filename)
    
    # 分析验证流程
    verification_pass = analyze_verification_flow(filename)
    
    print(f"\n{'='*50}")
    if theory_correct:
        print("✅ 验证理论正确！")
    else:
        print("❌ 验证理论需要进一步调整")
    
    if verification_pass:
        print("✅ 官方文件验证通过")
    else:
        print("🔥 发现验证漏洞点！")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
