#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证书格式深度分析工具
重新分析DHTB证书的完整结构
"""

import struct
import hashlib
import sys

def analyze_certificate_structure(filename):
    """深度分析证书结构"""
    print(f"=== 证书结构深度分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基本信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 逐字节分析证书头部
    print(f"\n=== 证书头部分析 (前64字节) ===")
    for i in range(0, 64, 16):
        offset = cert_offset + i
        chunk = data[offset:offset + 16]
        hex_str = ' '.join(f'{b:02x}' for b in chunk)
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        print(f"0x{offset:08x}: {hex_str:<48} |{ascii_str}|")
    
    # 分析关键字段
    print(f"\n=== 关键字段解析 ===")
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"证书类型 (offset+0): 0x{cert_type:x}")
    print(f"密钥位长度 (offset+4): 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"e值 (offset+8): 0x{e_value:x}")
    
    # 分析模数区域
    print(f"\n=== 模数区域分析 (offset+12, 256字节) ===")
    modulus_start = cert_offset + 12
    modulus = data[modulus_start:modulus_start + 256]
    print(f"模数前16字节: {modulus[:16].hex()}")
    print(f"模数后16字节: {modulus[-16:].hex()}")
    
    # 分析哈希区域
    print(f"\n=== 哈希区域分析 ===")
    data_hash_offset = cert_offset + 268
    data_hash = data[data_hash_offset:data_hash_offset + 32]
    print(f"数据哈希 (offset+268): {data_hash.hex()}")
    
    if cert_type == 1:
        key_hash_offset = cert_offset + 300
        key_hash = data[key_hash_offset:key_hash_offset + 32]
        print(f"密钥哈希 (offset+300): {key_hash.hex()}")
    
    # 分析签名区域
    print(f"\n=== 签名区域分析 ===")
    if cert_type == 1:
        signature_offset = cert_offset + 340
        signature_size = 256  # 假设2048位RSA
    else:
        signature_offset = cert_offset + 308
        signature_size = 256
    
    signature = data[signature_offset:signature_offset + signature_size]
    print(f"签名偏移: 0x{signature_offset:x}")
    print(f"签名前16字节: {signature[:16].hex()}")
    print(f"签名后16字节: {signature[-16:].hex()}")
    
    return {
        'cert_offset': cert_offset,
        'cert_type': cert_type,
        'key_bit_len': key_bit_len,
        'e_value': e_value,
        'modulus': modulus,
        'data_hash': data_hash,
        'key_hash': key_hash if cert_type == 1 else None,
        'signature': signature
    }

def test_different_hash_combinations(cert_info):
    """测试不同的哈希组合方式"""
    print(f"\n=== 测试不同哈希组合 ===")
    
    cert_offset = cert_info['cert_offset']
    key_bit_len = cert_info['key_bit_len']
    e_value = cert_info['e_value']
    modulus = cert_info['modulus']
    expected_hash = cert_info['key_hash']
    
    if expected_hash is None:
        print("Type 0证书，跳过密钥哈希测试")
        return
    
    print(f"目标哈希: {expected_hash.hex()}")
    
    # 测试1: 标准组合 (key_bit_len + e + modulus)
    combo1 = struct.pack('<I', key_bit_len) + struct.pack('<I', e_value) + modulus
    hash1 = hashlib.sha256(combo1).digest()
    print(f"组合1 (位长+e+模数): {hash1.hex()[:16]}... 匹配: {'YES' if hash1 == expected_hash else 'NO'}")
    
    # 测试2: 大端序组合
    combo2 = struct.pack('>I', key_bit_len) + struct.pack('>I', e_value) + modulus
    hash2 = hashlib.sha256(combo2).digest()
    print(f"组合2 (大端序): {hash2.hex()[:16]}... 匹配: {'YES' if hash2 == expected_hash else 'NO'}")
    
    # 测试3: 只有模数
    hash3 = hashlib.sha256(modulus).digest()
    print(f"组合3 (仅模数): {hash3.hex()[:16]}... 匹配: {'YES' if hash3 == expected_hash else 'NO'}")
    
    # 测试4: e + 模数
    combo4 = struct.pack('<I', e_value) + modulus
    hash4 = hashlib.sha256(combo4).digest()
    print(f"组合4 (e+模数): {hash4.hex()[:16]}... 匹配: {'YES' if hash4 == expected_hash else 'NO'}")
    
    # 测试5: 模数的逆序
    combo5 = modulus[::-1]
    hash5 = hashlib.sha256(combo5).digest()
    print(f"组合5 (模数逆序): {hash5.hex()[:16]}... 匹配: {'YES' if hash5 == expected_hash else 'NO'}")
    
    # 测试6: 不同长度的组合
    for extra_bytes in [0, 4, 8, 12, 16]:
        if extra_bytes == 0:
            combo = struct.pack('<I', key_bit_len) + struct.pack('<I', e_value) + modulus
        else:
            # 从证书中读取额外字节
            with open(sys.argv[1], 'rb') as f:
                data = f.read()
            extra_data = data[cert_offset + 12 + 256:cert_offset + 12 + 256 + extra_bytes]
            combo = struct.pack('<I', key_bit_len) + struct.pack('<I', e_value) + modulus + extra_data
        
        hash_test = hashlib.sha256(combo).digest()
        match = hash_test == expected_hash
        print(f"组合6.{extra_bytes} (+{extra_bytes}字节): {hash_test.hex()[:16]}... 匹配: {'YES' if match else 'NO'}")
        if match:
            print(f"🎉 找到匹配！额外字节: {extra_bytes}")
            return True
    
    return False

def analyze_assembly_logic():
    """分析汇编代码中的哈希计算逻辑"""
    print(f"\n=== 汇编逻辑分析 ===")
    print("根据IDA Pro汇编代码分析:")
    print("1. 0x9f009e80: ADD X0, X19, #4     ; cert_address + 4")
    print("2. 0x9f009e8c: MOV W1, #0x108     ; 264字节")
    print("3. 0x9f009e94: BL sha256_hash_wrapper")
    print("4. 0x9f009eb8: BL memcmp_custom    ; 比较计算的哈希与输入哈希")
    print()
    print("这表明SPL确实是计算 sha256(cert_address + 4, 264字节)")
    print("但我们的计算结果与证书中存储的不匹配...")
    print()
    print("可能的原因:")
    print("1. 证书中的哈希不是密钥哈希，而是其他数据的哈希")
    print("2. 存在我们未发现的数据预处理步骤")
    print("3. 哈希算法实现有特殊之处")

def main():
    if len(sys.argv) != 2:
        print("用法: python certificate_format_analyzer.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    # 分析证书结构
    cert_info = analyze_certificate_structure(filename)
    
    # 测试不同哈希组合
    found_match = test_different_hash_combinations(cert_info)
    
    # 分析汇编逻辑
    analyze_assembly_logic()
    
    if found_match:
        print(f"\n✅ 找到正确的哈希计算方法！")
    else:
        print(f"\n❌ 未找到匹配的哈希计算方法")
        print("建议进一步分析汇编代码或检查数据预处理步骤")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
