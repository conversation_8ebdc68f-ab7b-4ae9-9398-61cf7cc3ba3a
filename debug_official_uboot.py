#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试官方uboot的密钥哈希计算
尝试找到正确的计算方法
"""

import struct
import hashlib
import sys

def debug_official_uboot(filename):
    """调试官方uboot的计算方法"""
    print(f"=== 调试官方uboot: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"  证书类型: {cert_type}")
    print(f"  密钥位长度: {key_bit_len}")
    print(f"  e值: 0x{e_value:x}")
    
    # 读取期望的密钥哈希
    expected_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"\n期望密钥哈希 (cert+0x12c): {expected_hash.hex()}")
    
    print(f"\n=== 尝试各种计算方法 ===")
    
    # 方法1: 原始方法 - SHA256(cert+4, 264字节)
    method_num = 1
    key_data_1 = data[cert_offset + 4:cert_offset + 4 + 264]
    hash_1 = hashlib.sha256(key_data_1).digest()
    match_1 = hash_1 == expected_hash
    print(f"方法{method_num}: SHA256(cert+4, 264字节) = {hash_1.hex()} {'✅' if match_1 else '❌'}")
    if match_1:
        return True, method_num
    
    # 方法2: 包含证书类型
    method_num += 1
    cert_data_2 = data[cert_offset:cert_offset + 268]
    hash_2 = hashlib.sha256(cert_data_2).digest()
    match_2 = hash_2 == expected_hash
    print(f"方法{method_num}: SHA256(cert+0, 268字节) = {hash_2.hex()} {'✅' if match_2 else '❌'}")
    if match_2:
        return True, method_num
    
    # 方法3: 只计算密钥部分 (不包含payload哈希)
    method_num += 1
    key_only_3 = data[cert_offset + 4:cert_offset + 4 + 260]  # 4+256 = 260
    hash_3 = hashlib.sha256(key_only_3).digest()
    match_3 = hash_3 == expected_hash
    print(f"方法{method_num}: SHA256(cert+4, 260字节) = {hash_3.hex()} {'✅' if match_3 else '❌'}")
    if match_3:
        return True, method_num
    
    # 方法4: 从密钥长度开始
    method_num += 1
    from_keylen_4 = data[cert_offset + 4:cert_offset + 4 + 256 + 4]  # keylen + e + modulus
    hash_4 = hashlib.sha256(from_keylen_4).digest()
    match_4 = hash_4 == expected_hash
    print(f"方法{method_num}: SHA256(keylen+e+modulus, 260字节) = {hash_4.hex()} {'✅' if match_4 else '❌'}")
    if match_4:
        return True, method_num
    
    # 方法5: 只计算模数
    method_num += 1
    modulus_only_5 = data[cert_offset + 12:cert_offset + 12 + 256]
    hash_5 = hashlib.sha256(modulus_only_5).digest()
    match_5 = hash_5 == expected_hash
    print(f"方法{method_num}: SHA256(模数, 256字节) = {hash_5.hex()} {'✅' if match_5 else '❌'}")
    if match_5:
        return True, method_num
    
    # 方法6: e值+模数
    method_num += 1
    e_modulus_6 = data[cert_offset + 8:cert_offset + 8 + 4 + 256]
    hash_6 = hashlib.sha256(e_modulus_6).digest()
    match_6 = hash_6 == expected_hash
    print(f"方法{method_num}: SHA256(e值+模数, 260字节) = {hash_6.hex()} {'✅' if match_6 else '❌'}")
    if match_6:
        return True, method_num
    
    # 方法7: 尝试不同的长度
    for length in [256, 260, 261, 262, 263, 265, 266, 267, 268]:
        method_num += 1
        test_data = data[cert_offset + 4:cert_offset + 4 + length]
        if cert_offset + 4 + length <= len(data):
            hash_test = hashlib.sha256(test_data).digest()
            match_test = hash_test == expected_hash
            print(f"方法{method_num}: SHA256(cert+4, {length}字节) = {hash_test.hex()[:16]}... {'✅' if match_test else '❌'}")
            if match_test:
                return True, method_num
    
    # 方法8: 尝试不同的起始位置
    for start in [0, 1, 2, 3, 5, 6, 7, 8, 12]:
        method_num += 1
        test_data = data[cert_offset + start:cert_offset + start + 264]
        if cert_offset + start + 264 <= len(data):
            hash_test = hashlib.sha256(test_data).digest()
            match_test = hash_test == expected_hash
            print(f"方法{method_num}: SHA256(cert+{start}, 264字节) = {hash_test.hex()[:16]}... {'✅' if match_test else '❌'}")
            if match_test:
                return True, method_num
    
    # 方法9: 尝试包含payload哈希的计算
    method_num += 1
    with_payload_hash = data[cert_offset + 4:cert_offset + 4 + 264 + 32]  # 包含payload哈希
    hash_9 = hashlib.sha256(with_payload_hash).digest()
    match_9 = hash_9 == expected_hash
    print(f"方法{method_num}: SHA256(cert+4, 296字节含payload哈希) = {hash_9.hex()[:16]}... {'✅' if match_9 else '❌'}")
    if match_9:
        return True, method_num
    
    # 方法10: 尝试双重哈希
    method_num += 1
    double_hash = hashlib.sha256(hash_1).digest()
    match_10 = double_hash == expected_hash
    print(f"方法{method_num}: SHA256(SHA256(cert+4, 264)) = {double_hash.hex()[:16]}... {'✅' if match_10 else '❌'}")
    if match_10:
        return True, method_num
    
    return False, 0

def compare_two_uboots():
    """比较两个uboot文件"""
    print(f"\n=== 比较uboot和uboot3 ===")
    
    files = ['uboot', 'uboot3']
    results = {}
    
    for filename in files:
        try:
            print(f"\n--- 分析 {filename} ---")
            found, method = debug_official_uboot(filename)
            results[filename] = (found, method)
            if found:
                print(f"🎉 {filename}: 找到匹配方法 {method}")
            else:
                print(f"❌ {filename}: 没有找到匹配方法")
        except Exception as e:
            print(f"❌ {filename}: 分析失败 - {e}")
            results[filename] = (False, 0)
    
    print(f"\n=== 比较结果 ===")
    for filename, (found, method) in results.items():
        status = f"方法{method}" if found else "未找到"
        print(f"  {filename}: {status}")

def main():
    if len(sys.argv) < 2:
        print("用法: python debug_official_uboot.py <uboot文件> [比较模式]")
        print("比较模式: 使用 'compare' 来比较uboot和uboot3")
        return 1
    
    if len(sys.argv) > 2 and sys.argv[2] == 'compare':
        compare_two_uboots()
    else:
        filename = sys.argv[1]
        print("调试官方uboot密钥哈希计算")
        print("="*60)
        
        found, method = debug_official_uboot(filename)
        
        print(f"\n{'='*60}")
        if found:
            print(f"🎉 找到正确的计算方法：方法{method}")
            print(f"这确认了{filename}是官方uboot")
        else:
            print(f"❌ 没有找到匹配的计算方法")
            print(f"需要进一步分析算法")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
