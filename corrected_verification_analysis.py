#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的验证逻辑分析
基于重新理解的汇编代码逻辑
"""

import struct
import hashlib
import sys

def analyze_corrected_verification(filename):
    """分析修正后的验证逻辑"""
    print(f"=== 修正的验证逻辑分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基本信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"Payload大小: 0x{payload_size:x}")
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 步骤1: 计算payload数据哈希
    print(f"\n=== 步骤1: 计算payload数据哈希 ===")
    
    # 根据汇编代码：get_payload_data_address返回证书地址
    # 然后读取证书+0x10位置的长度值
    hash_length_pos = cert_offset + 0x10
    hash_length = struct.unpack('<I', data[hash_length_pos:hash_length_pos + 4])[0]
    
    print(f"哈希长度位置: 0x{hash_length_pos:x}")
    print(f"哈希长度值: 0x{hash_length:x} ({hash_length} bytes)")
    
    # 计算从证书地址开始的哈希
    calculated_payload_hash = hashlib.sha256(data[cert_offset:cert_offset + hash_length]).digest()
    print(f"计算的payload哈希: {calculated_payload_hash.hex()}")
    
    # 步骤2: 读取证书中存储的哈希值
    print(f"\n=== 步骤2: 证书中的哈希值 ===")
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    data_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"证书类型: {cert_type}")
    print(f"证书中数据哈希 (+268): {data_hash.hex()}")
    
    if cert_type == 1:
        stored_hash_300 = data[cert_offset + 300:cert_offset + 300 + 32]
        print(f"证书中+300哈希: {stored_hash_300.hex()}")
    
    # 步骤3: 计算密钥哈希
    print(f"\n=== 步骤3: 计算密钥哈希 ===")
    
    # 按照RSA验证函数的逻辑
    key_data_start = cert_offset + 4
    key_data_length = 0x108  # 264字节
    key_data = data[key_data_start:key_data_start + key_data_length]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"密钥数据范围: 0x{key_data_start:x} - 0x{key_data_start + key_data_length:x}")
    print(f"计算的密钥哈希: {calculated_key_hash.hex()}")
    
    # 步骤4: 验证逻辑分析
    print(f"\n=== 步骤4: 验证逻辑分析 ===")
    
    print("根据汇编代码分析，验证流程应该是：")
    print("1. 计算payload数据哈希 (从证书地址开始)")
    print("2. 将此哈希作为'预期密钥哈希'传递给RSA验证")
    print("3. RSA验证函数计算实际密钥哈希")
    print("4. 比较两个哈希值")
    
    # 比较结果
    payload_hash_match = calculated_payload_hash == calculated_key_hash
    print(f"\nPayload哈希 vs 密钥哈希: {'MATCH' if payload_hash_match else 'NO MATCH'}")
    
    if cert_type == 1:
        payload_vs_300 = calculated_payload_hash == stored_hash_300
        key_vs_300 = calculated_key_hash == stored_hash_300
        print(f"Payload哈希 vs 证书+300: {'MATCH' if payload_vs_300 else 'NO MATCH'}")
        print(f"密钥哈希 vs 证书+300: {'MATCH' if key_vs_300 else 'NO MATCH'}")
    
    # 步骤5: 尝试不同的payload哈希计算
    print(f"\n=== 步骤5: 尝试不同的payload哈希计算 ===")
    
    # 方法1: 从0x200开始计算（原始payload）
    original_payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    print(f"原始payload哈希: {original_payload_hash.hex()}")
    
    # 方法2: 不同长度的证书数据
    for length in [256, 264, 268, 272, 300, 332, 340]:
        if cert_offset + length <= len(data):
            test_hash = hashlib.sha256(data[cert_offset:cert_offset + length]).digest()
            match_key = test_hash == calculated_key_hash
            if cert_type == 1:
                match_300 = test_hash == stored_hash_300
                print(f"证书数据({length}字节): {test_hash.hex()[:16]}... vs密钥: {'MATCH' if match_key else 'NO'} vs+300: {'MATCH' if match_300 else 'NO'}")
            else:
                print(f"证书数据({length}字节): {test_hash.hex()[:16]}... vs密钥: {'MATCH' if match_key else 'NO'}")
            
            if match_key:
                print(f"🎉 找到匹配！证书数据长度: {length} 字节")
                return True
    
    return False

def test_alternative_interpretations(filename):
    """测试其他可能的解释"""
    print(f"\n=== 测试其他可能的解释 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 也许证书+300存储的是预期的密钥哈希，而不是payload哈希
    print("假设: 证书+300存储预期的密钥哈希")
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    if cert_type == 1:
        expected_key_hash = data[cert_offset + 300:cert_offset + 300 + 32]
        print(f"预期密钥哈希: {expected_key_hash.hex()}")
        
        # 尝试找到能产生这个哈希的数据
        print("尝试找到能产生此哈希的数据...")
        
        # 测试不同的数据组合
        key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
        e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
        
        # 尝试不同的起始位置和长度
        for start_offset in [0, 4, 8, 12, 16]:
            for length in [256, 260, 264, 268, 272]:
                start_pos = cert_offset + start_offset
                if start_pos + length <= len(data):
                    test_data = data[start_pos:start_pos + length]
                    test_hash = hashlib.sha256(test_data).digest()
                    if test_hash == expected_key_hash:
                        print(f"🎉 找到匹配！起始偏移: +{start_offset}, 长度: {length}")
                        print(f"数据前16字节: {test_data[:16].hex()}")
                        return True
    
    print("未找到匹配的数据组合")
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python corrected_verification_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    # 分析修正后的验证逻辑
    found1 = analyze_corrected_verification(filename)
    
    # 测试其他解释
    found2 = test_alternative_interpretations(filename)
    
    if found1 or found2:
        print(f"\n✅ 找到正确的验证逻辑！")
    else:
        print(f"\n❌ 仍未找到正确的验证逻辑")
        print("可能需要更深入的汇编代码分析")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
