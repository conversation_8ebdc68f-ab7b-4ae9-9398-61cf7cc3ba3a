#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的密钥哈希分析
基于重新理解的汇编代码
"""

import struct
import hashlib
import sys

def correct_key_hash_analysis(filename):
    """正确的密钥哈希分析"""
    print(f"=== 正确的密钥哈希分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    print(f"\n=== 重新分析汇编代码 ===")
    print(f"关键汇编指令:")
    print(f"  0x9f009e80: ADD X0, X19, #4      ; 计算数据起始 = cert + 4")
    print(f"  0x9f009e84: ADD X22, X19, #0x10C ; 比较目标 = cert + 0x10C (268)")
    print(f"  0x9f009e8c: MOV W1, #0x108       ; 计算长度 = 264")
    print(f"  0x9f009e94: BL sha256_hash_wrapper ; 计算SHA256")
    print(f"  0x9f009e9c: MOV X1, X22          ; 比较目标 = cert + 268")
    print(f"  0x9f009ea4: BL memcmp_custom     ; 比较哈希")
    
    print(f"\n=== 正确的验证逻辑 ===")
    
    # 计算: SHA256(cert + 4, 264字节)
    key_data_start = cert_offset + 4
    key_data_length = 264
    key_data = data[key_data_start:key_data_start + key_data_length]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    # 比较: cert + 268位置的32字节
    stored_key_hash_pos = cert_offset + 268
    stored_key_hash = data[stored_key_hash_pos:stored_key_hash_pos + 32]
    
    print(f"计算方法: SHA256(cert + 4, 264字节)")
    print(f"比较目标: cert + 268位置的32字节")
    print(f"")
    print(f"计算结果: {calculated_key_hash.hex()}")
    print(f"存储值:   {stored_key_hash.hex()}")
    print(f"匹配:     {'YES' if calculated_key_hash == stored_key_hash else 'NO'}")
    
    # 检查cert+268位置存储的是什么
    print(f"\n=== cert+268位置分析 ===")
    print(f"cert+268存储的内容: {stored_key_hash.hex()}")
    
    # 这应该是payload的哈希！
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    print(f"Payload哈希:       {payload_hash.hex()}")
    print(f"匹配:              {'YES' if payload_hash == stored_key_hash else 'NO'}")
    
    if payload_hash == stored_key_hash:
        print(f"✅ cert+268存储的是payload哈希！")
        print(f"这意味着密钥哈希验证实际上是在验证payload哈希！")
    
    # 那么cert+0x12c存储的是什么？
    print(f"\n=== cert+0x12c位置分析 ===")
    cert_0x12c_data = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"cert+0x12c存储的内容: {cert_0x12c_data.hex()}")
    
    # 检查这是否是真正的密钥哈希
    if calculated_key_hash == cert_0x12c_data:
        print(f"✅ cert+0x12c存储的是真正的密钥哈希！")
    else:
        print(f"❌ cert+0x12c不是计算出的密钥哈希")
        print(f"可能是其他用途的数据")
    
    return calculated_key_hash == stored_key_hash

def analyze_certificate_layout(filename):
    """分析证书布局"""
    print(f"\n=== 证书布局分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 证书结构分析
    layout = [
        (0x000, 4, "证书类型"),
        (0x004, 4, "密钥位长"),
        (0x008, 4, "e值"),
        (0x00c, 256, "RSA模数"),
        (0x10c, 32, "数据哈希 (payload哈希)"),
        (0x12c, 32, "密钥哈希 (真正的密钥哈希?)"),
        (0x14c, 256, "RSA签名"),
    ]
    
    print(f"证书布局 (起始: 0x{cert_offset:x}):")
    for offset, size, description in layout:
        pos = cert_offset + offset
        if pos + min(size, 16) <= len(data):
            sample = data[pos:pos + min(size, 16)]
            print(f"  +0x{offset:03x}: {description}")
            print(f"         {sample.hex()}")
            if size > 16:
                print(f"         ... (总共{size}字节)")
    
    # 验证我们的理解
    print(f"\n=== 验证理解 ===")
    
    # 1. cert+0x10c应该是payload哈希
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    cert_0x10c = data[cert_offset + 0x10c:cert_offset + 0x10c + 32]
    print(f"1. cert+0x10c vs payload哈希: {'✅ 匹配' if payload_hash == cert_0x10c else '❌ 不匹配'}")
    
    # 2. cert+0x12c应该是密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    cert_0x12c = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"2. cert+0x12c vs 计算的密钥哈希: {'✅ 匹配' if calculated_key_hash == cert_0x12c else '❌ 不匹配'}")
    
    # 3. 汇编代码中的比较
    print(f"3. 汇编代码比较的是: cert+268 (0x10c) = payload哈希")
    print(f"   这解释了为什么我们之前的分析有问题！")

def main():
    if len(sys.argv) != 2:
        print("用法: python correct_key_hash_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("正确的密钥哈希分析")
    print("="*50)
    
    # 1. 正确的密钥哈希分析
    success = correct_key_hash_analysis(filename)
    
    # 2. 分析证书布局
    analyze_certificate_layout(filename)
    
    print(f"\n{'='*50}")
    print(f"关键发现:")
    print(f"1. 汇编代码中的'密钥哈希验证'实际上是在验证payload哈希")
    print(f"2. cert+268存储payload哈希，cert+0x12c存储真正的密钥哈希")
    print(f"3. 我们之前的理解有误，需要重新分析验证流程")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
