#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暴力匹配脚本 - 尝试各种方法找到正确的hash key计算方式
"""

import struct
import hashlib
import sys
import itertools

def brute_force_hash_match(filename):
    """暴力匹配hash key计算方法"""
    print(f"=== 暴力匹配hash key计算方法 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 目标值 - 前16字节
    target_full = bytes.fromhex('2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e')
    target_part1 = target_full[:16]  # 前16字节
    target_part2 = target_full[16:]  # 后16字节
    
    print(f"目标完整值: {target_full.hex()}")
    print(f"目标前16字节: {target_part1.hex()}")
    print(f"目标后16字节: {target_part2.hex()}")
    
    print(f"\n基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 准备各种数据源
    data_sources = {
        'cert_full': data[cert_offset:cert_offset + 596],  # 完整证书
        'cert_header': data[cert_offset:cert_offset + 12],  # 证书头
        'cert_key_data': data[cert_offset + 4:cert_offset + 4 + 264],  # 密钥数据
        'cert_modulus': data[cert_offset + 12:cert_offset + 12 + 256],  # 模数
        'cert_to_hash': data[cert_offset:cert_offset + 268],  # 到hash位置
        'cert_to_key': data[cert_offset:cert_offset + 300],  # 到key位置
        'payload_data': data[0x200:0x200 + payload_size],  # payload数据
        'payload_hash': data[cert_offset + 268:cert_offset + 268 + 32],  # payload哈希
        'file_header': data[0:0x200],  # 文件头
        'dhtb_header': data[0:52],  # DHTB头
    }
    
    # 尝试单一数据源
    print(f"\n=== 测试单一数据源 ===")
    for name, source_data in data_sources.items():
        if len(source_data) > 0:
            hash_result = hashlib.sha256(source_data).digest()
            
            # 检查完整匹配
            if hash_result == target_full:
                print(f"✅ 完整匹配: {name}")
                print(f"   数据长度: {len(source_data)}")
                return True
            
            # 检查前16字节匹配
            if hash_result[:16] == target_part1:
                print(f"✅ 前16字节匹配: {name}")
                print(f"   数据长度: {len(source_data)}")
                print(f"   完整哈希: {hash_result.hex()}")
            
            # 检查后16字节匹配
            if hash_result[16:] == target_part2:
                print(f"✅ 后16字节匹配: {name}")
                print(f"   数据长度: {len(source_data)}")
                print(f"   完整哈希: {hash_result.hex()}")
    
    # 尝试数据组合
    print(f"\n=== 测试数据组合 ===")
    source_names = list(data_sources.keys())
    
    # 两两组合
    for name1, name2 in itertools.combinations(source_names, 2):
        data1 = data_sources[name1]
        data2 = data_sources[name2]
        
        if len(data1) > 0 and len(data2) > 0:
            # 组合1: data1 + data2
            combined = data1 + data2
            if len(combined) < 10000:  # 避免太大的数据
                hash_result = hashlib.sha256(combined).digest()
                
                if hash_result == target_full:
                    print(f"✅ 完整匹配: {name1} + {name2}")
                    return True
                
                if hash_result[:16] == target_part1:
                    print(f"✅ 前16字节匹配: {name1} + {name2}")
                    print(f"   完整哈希: {hash_result.hex()}")
                
                if hash_result[16:] == target_part2:
                    print(f"✅ 后16字节匹配: {name1} + {name2}")
                    print(f"   完整哈希: {hash_result.hex()}")
    
    # 尝试不同的数据范围
    print(f"\n=== 测试不同数据范围 ===")
    
    # 从证书开始的各种长度
    for start_offset in [0, 4, 8, 12, 16]:
        for length in [32, 64, 128, 256, 260, 264, 268, 300, 332, 340, 596]:
            start_pos = cert_offset + start_offset
            if start_pos + length <= len(data):
                test_data = data[start_pos:start_pos + length]
                hash_result = hashlib.sha256(test_data).digest()
                
                if hash_result == target_full:
                    print(f"✅ 完整匹配: cert+{start_offset}, {length}字节")
                    return True
                
                if hash_result[:16] == target_part1:
                    print(f"✅ 前16字节匹配: cert+{start_offset}, {length}字节")
                    print(f"   完整哈希: {hash_result.hex()}")
                
                if hash_result[16:] == target_part2:
                    print(f"✅ 后16字节匹配: cert+{start_offset}, {length}字节")
                    print(f"   完整哈希: {hash_result.hex()}")
    
    # 尝试字节序转换
    print(f"\n=== 测试字节序转换 ===")
    
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    
    # 转换各种字段的字节序
    test_data = bytearray(key_data)
    
    # 转换密钥长度和e值
    key_len = struct.unpack('<I', test_data[0:4])[0]
    e_val = struct.unpack('<I', test_data[4:8])[0]
    
    # 大端序
    test_data[0:4] = struct.pack('>I', key_len)
    test_data[4:8] = struct.pack('>I', e_val)
    hash_result = hashlib.sha256(test_data).digest()
    
    if hash_result[:16] == target_part1:
        print(f"✅ 前16字节匹配: 密钥数据(大端序)")
        print(f"   完整哈希: {hash_result.hex()}")
    
    # 尝试反转模数字节序
    modulus = data[cert_offset + 12:cert_offset + 12 + 256]
    reversed_modulus = modulus[::-1]  # 反转字节序
    hash_result = hashlib.sha256(reversed_modulus).digest()
    
    if hash_result[:16] == target_part1:
        print(f"✅ 前16字节匹配: 反转模数")
        print(f"   完整哈希: {hash_result.hex()}")
    
    # 尝试特殊组合
    print(f"\n=== 测试特殊组合 ===")
    
    # 密钥数据 + 特定常量
    constants = [b'\x00' * 32, b'\xff' * 32, struct.pack('<I', payload_size)]
    
    for const in constants:
        combined = key_data + const
        hash_result = hashlib.sha256(combined).digest()
        
        if hash_result[:16] == target_part1:
            print(f"✅ 前16字节匹配: 密钥数据 + 常量")
            print(f"   常量: {const.hex()}")
            print(f"   完整哈希: {hash_result.hex()}")
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python brute_force_hash_match.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("暴力匹配hash key计算方法")
    print("="*60)
    
    found = brute_force_hash_match(filename)
    
    print(f"\n{'='*60}")
    if found:
        print("🎉 找到了正确的计算方法！")
    else:
        print("❌ 未找到匹配的计算方法")
        print("可能需要尝试更复杂的算法或数据组合")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
