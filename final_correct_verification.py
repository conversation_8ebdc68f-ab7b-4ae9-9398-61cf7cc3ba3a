#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终正确的验证方法
基于完整IDA反汇编分析
"""

import struct
import hashlib
import sys

def final_verification_analysis(filename):
    """最终正确的验证分析"""
    print(f"=== 最终正确的验证分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"  证书类型: {cert_type}")
    
    print(f"\n=== 完整验证流程分析 ===")
    print(f"基于IDA反汇编的完整调用链:")
    print(f"  1. check_dhtb_header_and_hash()")
    print(f"     - 检查DHTB magic (0x42544844)")
    print(f"     - 调用 get_hash_from_cert() -> calculate_payload_hash()")
    print(f"     - 从 cert+300 读取期望密钥哈希")
    print(f"")
    print(f"  2. verify_signature_and_position(期望哈希, payload_data)")
    print(f"     - 检查位置字节 (payload_size + 512) == 0")
    print(f"     - 计算 payload SHA256")
    print(f"     - 调用 rsa_signature_verify(期望哈希, payload哈希, cert)")
    print(f"")
    print(f"  3. rsa_signature_verify(期望哈希, payload哈希, cert)")
    print(f"     - 计算实际密钥哈希: SHA256(cert+4, 264字节)")
    print(f"     - 比较: 期望哈希 vs 实际哈希")
    print(f"     - 比较: payload哈希 vs cert+268存储的哈希")
    
    print(f"\n=== 关键发现 ===")
    print(f"期望密钥哈希来源: cert+300 (32字节)")
    print(f"实际密钥哈希计算: SHA256(cert+4, 264字节)")
    
    # 读取期望的密钥哈希 (从cert+300)
    expected_key_hash = data[cert_offset + 300:cert_offset + 300 + 32]
    print(f"\n期望密钥哈希 (cert+300): {expected_key_hash.hex()}")
    
    # 计算实际的密钥哈希 (SHA256(cert+4, 264字节))
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    print(f"计算密钥哈希 (SHA256(cert+4, 264)): {calculated_key_hash.hex()}")
    
    # 验证匹配
    key_hash_match = expected_key_hash == calculated_key_hash
    print(f"\n=== 密钥哈希验证 ===")
    print(f"密钥哈希匹配: {'✅ PASS' if key_hash_match else '❌ FAIL'}")
    
    # 额外验证：payload哈希
    print(f"\n=== Payload哈希验证 ===")
    payload_data = data[0x200:0x200 + payload_size]
    calculated_payload_hash = hashlib.sha256(payload_data).digest()
    stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"计算Payload哈希: {calculated_payload_hash.hex()}")
    print(f"存储Payload哈希 (cert+268): {stored_payload_hash.hex()}")
    
    payload_hash_match = calculated_payload_hash == stored_payload_hash
    print(f"Payload哈希匹配: {'✅ PASS' if payload_hash_match else '❌ FAIL'}")
    
    # 位置字节检查
    print(f"\n=== 位置字节检查 ===")
    position_byte_offset = payload_size + 512
    if position_byte_offset < len(data):
        position_byte = data[position_byte_offset]
        print(f"位置字节 (offset {position_byte_offset}): 0x{position_byte:02x}")
        position_check = position_byte == 0
        print(f"位置字节检查: {'✅ PASS' if position_check else '❌ FAIL'}")
    else:
        position_check = False
        print(f"位置字节检查: ❌ FAIL (超出文件范围)")
    
    # 最终结论
    print(f"\n=== 最终验证结果 ===")
    all_checks_pass = key_hash_match and payload_hash_match and position_check
    
    if all_checks_pass:
        print(f"🎉 所有验证都通过！")
        print(f"✅ 这是官方uboot，所有哈希和检查都匹配")
        return True, "official"
    else:
        print(f"❌ 验证失败")
        if not key_hash_match:
            print(f"   - 密钥哈希不匹配")
        if not payload_hash_match:
            print(f"   - Payload哈希不匹配")
        if not position_check:
            print(f"   - 位置字节检查失败")
        print(f"❓ 这可能不是官方uboot，或者被修改过")
        return False, "modified"

def show_cert_structure(filename):
    """显示证书结构"""
    print(f"\n=== 证书结构详情 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书起始地址: 0x{cert_offset:x}")
    
    # 解析证书结构
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"  +0x000: 证书类型 = {cert_type}")
    print(f"  +0x004: 密钥位长度 = {key_bit_len}")
    print(f"  +0x008: e值 = 0x{e_value:x}")
    print(f"  +0x00C: 模数 (256字节)")
    print(f"  +0x10C: Payload哈希 (32字节)")
    print(f"  +0x12C: 密钥哈希存储 (32字节) - Type 1证书")
    print(f"  +0x14C: 其他数据...")
    print(f"  +0x12C: Type和Version (Type 0证书)")
    print(f"  +0x134: 签名数据 (Type 0证书)")
    print(f"  +0x12C: 期望密钥哈希位置 (根据IDA分析)")
    
    # 显示关键数据
    payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    key_hash_type1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    key_hash_expected = data[cert_offset + 300:cert_offset + 300 + 32]
    
    print(f"\n关键哈希数据:")
    print(f"  Payload哈希 (cert+268): {payload_hash.hex()}")
    print(f"  密钥哈希 Type1位置 (cert+0x12c): {key_hash_type1.hex()}")
    print(f"  期望密钥哈希 (cert+300): {key_hash_expected.hex()}")

def main():
    if len(sys.argv) != 2:
        print("用法: python final_correct_verification.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("最终正确的uboot验证分析")
    print("="*60)
    
    # 显示证书结构
    show_cert_structure(filename)
    
    # 进行最终验证
    is_official, status = final_verification_analysis(filename)
    
    print(f"\n{'='*60}")
    if is_official:
        print("🎉 验证完成：这是官方uboot！")
    else:
        print("❌ 验证完成：这不是官方uboot")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
