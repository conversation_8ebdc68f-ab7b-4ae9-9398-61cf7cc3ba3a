#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import hashlib
import sys
import os

def check_key_hash_verification(filename):
    """检查密钥哈希验证，逻辑与您的payload哈希脚本相同"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"文件读取错误: {e}")
        return

    # 提取必要参数 (与您的脚本相同的逻辑)
    try:
        payload_size = struct.unpack('<I', data[48:52])[0]
        wrong_check_pos = payload_size + 512
        position_bug_pass = (wrong_check_pos < len(data)) and (data[wrong_check_pos] == 0)
        
        cert_offset_pos = payload_size + 552
        cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    except:
        print("校验参数提取失败")
        return

    # 计算密钥哈希值 (需要找到正确的计算方法)
    try:
        # 获取预期密钥哈希值 (基于我们的发现: cert+0x12c + cert+0x13c)
        expected_key_hash_part1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
        expected_key_hash_part2 = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
        expected_key_hash = expected_key_hash_part1 + expected_key_hash_part2

        # 尝试不同的计算方法来匹配预期值
        calculated_key_hash = None
        calculation_method = "未找到"

        # 测试多种可能的计算方法
        test_methods = [
            (cert_offset + 4, 264, "SHA256(cert+4, 264字节)"),
            (cert_offset + 0, 264, "SHA256(cert+0, 264字节)"),
            (cert_offset + 8, 256, "SHA256(cert+8, 256字节)"),
            (cert_offset + 12, 252, "SHA256(cert+12, 252字节)"),
            (cert_offset + 4, 268, "SHA256(cert+4, 268字节)"),
            (cert_offset + 0, 268, "SHA256(cert+0, 268字节)"),
            (cert_offset + 4, 260, "SHA256(cert+4, 260字节)"),
        ]

        for start_pos, length, method_name in test_methods:
            if start_pos + length <= len(data):
                test_data = data[start_pos:start_pos + length]
                test_hash = hashlib.sha256(test_data).digest()
                if test_hash == expected_key_hash:
                    calculated_key_hash = test_hash
                    calculation_method = method_name
                    break

        # 如果没找到匹配的方法，使用默认的汇编代码方法
        if calculated_key_hash is None:
            key_hash_start = cert_offset + 4
            key_hash_length = 264
            if key_hash_start + key_hash_length > len(data):
                raise IndexError("密钥哈希计算范围超出文件大小")
            calculated_key_hash = hashlib.sha256(data[key_hash_start:key_hash_start + key_hash_length]).digest()
            calculation_method = "SHA256(cert+4, 264字节) [默认]"
        
    except Exception as e:
        print(f"密钥哈希计算错误: {e}")
        return

    # 输出结果 (与您的脚本相同的格式)
    print(f"计算密钥哈希值: {calculated_key_hash.hex()}")
    print(f"预期密钥哈希值: {expected_key_hash.hex()}")
    print(f"检验密钥哈希结果: {'通过' if calculated_key_hash == expected_key_hash else '不通过'}")

    # 额外信息: 显示构造细节
    print(f"\n密钥哈希构造详情:")
    print(f"  计算方法: {calculation_method}")
    print(f"  预期值来源: cert+0x12c (16字节) + cert+0x13c (16字节)")
    print(f"  cert+0x12c: {expected_key_hash_part1.hex()}")
    print(f"  cert+0x13c: {expected_key_hash_part2.hex()}")

def check_both_hashes(filename):
    """同时检查payload哈希和密钥哈希"""
    print("="*60)
    print("完整哈希验证检查")
    print("="*60)
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"文件读取错误: {e}")
        return

    # 提取基础参数
    try:
        payload_size = struct.unpack('<I', data[48:52])[0]
        cert_offset_pos = payload_size + 552
        cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
        
        print(f"基础参数:")
        print(f"  Payload大小: 0x{payload_size:x}")
        print(f"  证书偏移: 0x{cert_offset:x}")
        print()
        
    except:
        print("参数提取失败")
        return

    # 1. Payload哈希验证 (您的脚本逻辑)
    print("1. Payload哈希验证:")
    try:
        payload_hash_start = 0x200
        payload_hash_length = payload_size
        calculated_payload_hash = hashlib.sha256(data[payload_hash_start:payload_hash_start + payload_hash_length]).digest()
        expected_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
        
        print(f"   计算值: {calculated_payload_hash.hex()}")
        print(f"   预期值: {expected_payload_hash.hex()}")
        print(f"   结果: {'✅ 通过' if calculated_payload_hash == expected_payload_hash else '❌ 不通过'}")
        
    except Exception as e:
        print(f"   错误: {e}")
    
    print()
    
    # 2. 密钥哈希验证
    print("2. 密钥哈希验证:")
    try:
        key_hash_start = cert_offset + 4
        key_hash_length = 264
        calculated_key_hash = hashlib.sha256(data[key_hash_start:key_hash_start + key_hash_length]).digest()
        
        # 组合预期哈希
        expected_key_hash_part1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
        expected_key_hash_part2 = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
        expected_key_hash = expected_key_hash_part1 + expected_key_hash_part2
        
        print(f"   计算值: {calculated_key_hash.hex()}")
        print(f"   预期值: {expected_key_hash.hex()}")
        print(f"   结果: {'✅ 通过' if calculated_key_hash == expected_key_hash else '❌ 不通过'}")
        
    except Exception as e:
        print(f"   错误: {e}")
    
    print()
    
    # 3. 位置检查
    print("3. 位置检查:")
    try:
        position_check_offset = payload_size + 512
        if position_check_offset < len(data):
            position_byte = data[position_check_offset]
            print(f"   检查位置: 0x{position_check_offset:x}")
            print(f"   字节值: 0x{position_byte:02x}")
            print(f"   结果: {'✅ 通过' if position_byte == 0 else '❌ 不通过'}")
        else:
            print(f"   检查位置超出文件范围")
    except Exception as e:
        print(f"   错误: {e}")

def main():
    if len(sys.argv) != 2:
        print("用法: python key_hash_verifier.py <文件路径>")
        return 1
    
    input_file = sys.argv[1]
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return 1
    
    # 单独的密钥哈希检查 (与您的脚本格式相同)
    print("密钥哈希验证:")
    print("-" * 40)
    check_key_hash_verification(input_file)
    
    print("\n")
    
    # 完整的验证检查
    check_both_hashes(input_file)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
