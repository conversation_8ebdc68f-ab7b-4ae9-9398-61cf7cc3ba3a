#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试哈希计算 - 逐步验证每个步骤
"""

import struct
import hashlib
import sys

def debug_step_by_step(filename):
    """逐步调试哈希计算"""
    print(f"=== 逐步调试: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 步骤1: 基本信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"Payload size: 0x{payload_size:x}")
    print(f"证书偏移: 0x{cert_offset:x}")
    print(f"证书类型: {cert_type}")
    
    # 步骤2: 读取密钥数据的每个部分
    print(f"\n=== 密钥数据分析 ===")
    key_bit_len_pos = cert_offset + 4
    e_value_pos = cert_offset + 8
    modulus_pos = cert_offset + 12
    
    key_bit_len = struct.unpack('<I', data[key_bit_len_pos:key_bit_len_pos + 4])[0]
    e_value = struct.unpack('<I', data[e_value_pos:e_value_pos + 4])[0]
    
    print(f"密钥位长度位置: 0x{key_bit_len_pos:x}")
    print(f"密钥位长度值: 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"e值位置: 0x{e_value_pos:x}")
    print(f"e值: 0x{e_value:x}")
    print(f"模数位置: 0x{modulus_pos:x}")
    
    # 步骤3: 按照SPL逻辑计算密钥哈希
    print(f"\n=== SPL密钥哈希计算 ===")
    # SPL: sha256_hash_wrapper(cert_address + 4, 0x108, hash_buffer)
    key_data_start = cert_offset + 4
    key_data_length = 0x108  # 264字节
    
    print(f"密钥数据起始: 0x{key_data_start:x}")
    print(f"密钥数据长度: 0x{key_data_length:x} ({key_data_length} bytes)")
    
    key_data = data[key_data_start:key_data_start + key_data_length]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"密钥数据前16字节: {key_data[:16].hex()}")
    print(f"密钥数据后16字节: {key_data[-16:].hex()}")
    print(f"计算的密钥哈希: {calculated_key_hash.hex()}")
    
    # 步骤4: 读取证书中存储的哈希
    print(f"\n=== 证书中的哈希值 ===")
    stored_key_hash_pos = cert_offset + 300
    stored_key_hash = data[stored_key_hash_pos:stored_key_hash_pos + 32]
    
    print(f"存储的密钥哈希位置: 0x{stored_key_hash_pos:x}")
    print(f"存储的密钥哈希: {stored_key_hash.hex()}")
    
    # 步骤5: 验证
    print(f"\n=== 验证结果 ===")
    match = calculated_key_hash == stored_key_hash
    print(f"哈希匹配: {'YES' if match else 'NO'}")
    
    if not match:
        print(f"\n=== 差异分析 ===")
        for i in range(32):
            if calculated_key_hash[i] != stored_key_hash[i]:
                print(f"字节 {i}: 计算={calculated_key_hash[i]:02x}, 存储={stored_key_hash[i]:02x}")
    
    # 步骤6: 尝试不同的计算方法
    print(f"\n=== 尝试其他计算方法 ===")
    
    # 方法1: 只计算模数
    modulus_only = data[modulus_pos:modulus_pos + 256]
    modulus_hash = hashlib.sha256(modulus_only).digest()
    print(f"仅模数哈希: {modulus_hash.hex()}")
    print(f"仅模数匹配: {'YES' if modulus_hash == stored_key_hash else 'NO'}")
    
    # 方法2: 计算e值+模数
    e_and_modulus = data[e_value_pos:e_value_pos + 4 + 256]
    e_mod_hash = hashlib.sha256(e_and_modulus).digest()
    print(f"e+模数哈希: {e_mod_hash.hex()}")
    print(f"e+模数匹配: {'YES' if e_mod_hash == stored_key_hash else 'NO'}")
    
    # 方法3: 尝试不同长度
    for length in [260, 264, 268, 272]:
        test_data = data[key_data_start:key_data_start + length]
        test_hash = hashlib.sha256(test_data).digest()
        match = test_hash == stored_key_hash
        print(f"长度{length}字节哈希: {test_hash.hex()[:16]}... 匹配: {'YES' if match else 'NO'}")
        if match:
            print(f"🎉 找到匹配！长度: {length} 字节")
            break
    
    # 方法4: 尝试不同起始位置
    print(f"\n=== 尝试不同起始位置 ===")
    for offset in [0, 4, 8, 12]:
        start_pos = cert_offset + offset
        test_data = data[start_pos:start_pos + 264]
        test_hash = hashlib.sha256(test_data).digest()
        match = test_hash == stored_key_hash
        print(f"起始+{offset}哈希: {test_hash.hex()[:16]}... 匹配: {'YES' if match else 'NO'}")
        if match:
            print(f"🎉 找到匹配！起始偏移: +{offset}")
            break

def main():
    if len(sys.argv) != 2:
        print("用法: python debug_hash_calculation.py <uboot文件>")
        return 1
    
    debug_step_by_step(sys.argv[1])
    return 0

if __name__ == "__main__":
    sys.exit(main())
