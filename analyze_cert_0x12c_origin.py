#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析cert+0x12c位置值的来源
深入研究这个哈希值是如何计算出来的
"""

import struct
import hashlib
import sys

def analyze_cert_0x12c_origin(filename):
    """分析cert+0x12c位置值的来源"""
    print(f"=== 分析cert+0x12c值的来源 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 目标值
    target_value = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    print(f"目标值 (cert+0x12c): {target_value.hex()}")
    
    print(f"\n=== 尝试各种计算方法 ===")
    
    # 方法1: 证书数据的各种哈希
    print(f"1. 证书数据哈希测试:")
    
    cert_test_ranges = [
        (0, 16, "证书头部16字节"),
        (0, 32, "证书头部32字节"),
        (4, 264, "密钥数据(cert+4, 264)"),
        (12, 256, "RSA模数(cert+12, 256)"),
        (0, 268, "证书前268字节"),
        (0, 300, "证书前300字节"),
        (268, 32, "数据哈希字段"),
        (300, 32, "密钥哈希字段"),
    ]
    
    for start, length, description in cert_test_ranges:
        test_data = data[cert_offset + start:cert_offset + start + length]
        if len(test_data) == length:
            test_hash = hashlib.sha256(test_data).digest()
            match = test_hash[:16] == target_value
            print(f"   {description}: {'MATCH!' if match else 'NO'}")
            if match:
                print(f"     完整哈希: {test_hash.hex()}")
                return True
    
    # 方法2: Payload数据的哈希
    print(f"\n2. Payload数据哈希测试:")
    
    payload_test_ranges = [
        (0x200, payload_size, "完整payload"),
        (0x200, 256, "payload前256字节"),
        (0x200, 512, "payload前512字节"),
        (0x200, 1024, "payload前1024字节"),
        (0, 0x200, "文件头部"),
        (0, payload_size + 0x200, "头部+payload"),
    ]
    
    for start, length, description in payload_test_ranges:
        if start + length <= len(data):
            test_data = data[start:start + length]
            test_hash = hashlib.sha256(test_data).digest()
            match = test_hash[:16] == target_value
            print(f"   {description}: {'MATCH!' if match else 'NO'}")
            if match:
                print(f"     完整哈希: {test_hash.hex()}")
                return True
    
    # 方法3: 组合数据的哈希
    print(f"\n3. 组合数据哈希测试:")
    
    # 尝试各种数据组合
    combinations = [
        ("payload + 证书头", data[0x200:0x200 + payload_size] + data[cert_offset:cert_offset + 32]),
        ("文件头 + 证书", data[0:0x200] + data[cert_offset:cert_offset + 268]),
        ("密钥数据 + payload哈希", data[cert_offset + 4:cert_offset + 4 + 264] + data[cert_offset + 268:cert_offset + 268 + 32]),
    ]
    
    for description, test_data in combinations:
        test_hash = hashlib.sha256(test_data).digest()
        match = test_hash[:16] == target_value
        print(f"   {description}: {'MATCH!' if match else 'NO'}")
        if match:
            print(f"     完整哈希: {test_hash.hex()}")
            return True
    
    # 方法4: 特殊的数据处理
    print(f"\n4. 特殊数据处理测试:")
    
    # 尝试字节序转换
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    
    # 转换密钥长度和e值为大端序
    modified_data = bytearray(key_data)
    key_len = struct.unpack('<I', modified_data[0:4])[0]
    e_val = struct.unpack('<I', modified_data[4:8])[0]
    modified_data[0:4] = struct.pack('>I', key_len)
    modified_data[4:8] = struct.pack('>I', e_val)
    
    test_hash = hashlib.sha256(modified_data).digest()
    match = test_hash[:16] == target_value
    print(f"   密钥数据(大端序): {'MATCH!' if match else 'NO'}")
    if match:
        print(f"     完整哈希: {test_hash.hex()}")
        return True
    
    # 方法5: 搜索文件中是否有这个16字节的原始数据
    print(f"\n5. 搜索原始数据:")
    
    for i in range(len(data) - 15):
        if data[i:i + 16] == target_value:
            print(f"   找到原始数据在偏移: 0x{i:x}")
            # 检查这个位置前后的数据
            context_start = max(0, i - 32)
            context_end = min(len(data), i + 48)
            context = data[context_start:context_end]
            print(f"   上下文: {context.hex()}")
            
            # 检查是否是某个更大数据的哈希结果
            for j in range(max(0, i - 1000), i):
                for length in [16, 32, 64, 128, 256, 264, 268]:
                    if j + length <= len(data):
                        test_data = data[j:j + length]
                        test_hash = hashlib.sha256(test_data).digest()
                        if test_hash[:16] == target_value:
                            print(f"   可能来源: SHA256(offset 0x{j:x}, length {length})[:16]")
                            print(f"   完整哈希: {test_hash.hex()}")
                            return True
    
    return False

def analyze_rsa_key_structure(filename):
    """分析RSA密钥结构"""
    print(f"\n=== RSA密钥结构分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 解析RSA密钥结构
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"RSA密钥信息:")
    print(f"  证书类型: {cert_type}")
    print(f"  密钥长度: {key_bit_len} bits")
    print(f"  e值: 0x{e_value:x}")
    
    # 提取模数
    modulus = data[cert_offset + 12:cert_offset + 12 + 256]
    print(f"  模数 (前32字节): {modulus[:32].hex()}")
    print(f"  模数 (后32字节): {modulus[-32:].hex()}")
    
    # 尝试计算模数的哈希
    modulus_hash = hashlib.sha256(modulus).digest()
    target_value = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    
    print(f"\n模数哈希测试:")
    print(f"  SHA256(模数)[:16]: {modulus_hash[:16].hex()}")
    print(f"  目标值:           {target_value.hex()}")
    print(f"  匹配: {'YES' if modulus_hash[:16] == target_value else 'NO'}")
    
    if modulus_hash[:16] == target_value:
        print(f"🎉 找到了！cert+0x12c = SHA256(RSA模数)[:16]")
        return True
    
    # 尝试其他RSA相关的计算
    rsa_components = [
        ("密钥长度", struct.pack('<I', key_bit_len)),
        ("e值", struct.pack('<I', e_value)),
        ("密钥长度+e值", struct.pack('<I', key_bit_len) + struct.pack('<I', e_value)),
        ("e值+模数", struct.pack('<I', e_value) + modulus),
        ("密钥长度+e值+模数", struct.pack('<I', key_bit_len) + struct.pack('<I', e_value) + modulus),
    ]
    
    print(f"\nRSA组件哈希测试:")
    for name, component in rsa_components:
        comp_hash = hashlib.sha256(component).digest()
        match = comp_hash[:16] == target_value
        print(f"  {name}: {'MATCH!' if match else 'NO'}")
        if match:
            print(f"    完整哈希: {comp_hash.hex()}")
            return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python analyze_cert_0x12c_origin.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("分析cert+0x12c值的来源")
    print("="*50)
    
    # 1. 尝试各种计算方法
    found_method1 = analyze_cert_0x12c_origin(filename)
    
    # 2. 分析RSA密钥结构
    found_method2 = analyze_rsa_key_structure(filename)
    
    print(f"\n{'='*50}")
    if found_method1 or found_method2:
        print("✅ 找到了cert+0x12c值的计算方法！")
    else:
        print("❌ 未找到cert+0x12c值的计算方法")
        print("这个值可能是:")
        print("  1. 预先计算好的固定值")
        print("  2. 使用了未知的算法")
        print("  3. 来自外部数据源")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
