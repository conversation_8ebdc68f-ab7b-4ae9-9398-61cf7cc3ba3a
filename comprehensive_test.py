#!/usr/bin/env python3
"""综合Type 1绕过测试"""
import struct
import hashlib

def comprehensive_test():
    files_to_test = [
        "uboot3_fixed_type1_exploit",
        "uboot3_type1_hash_bypass_exploit", 
        "uboot3"
    ]
    
    for filename in files_to_test:
        try:
            print(f"\n测试文件: {filename}")
            with open(filename, 'rb') as f:
                data = f.read()
            
            payload_size = struct.unpack('<I', data[48:52])[0]
            cert_offset_pos = payload_size + 552
            cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
            cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
            
            print(f"  证书类型: {cert_type}")
            
            if cert_type == 1:
                payload_data = data[0x200:0x200 + payload_size]
                stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
                actual_payload_hash = hashlib.sha256(payload_data).digest()
                
                key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
                stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
                actual_key_hash = hashlib.sha256(key_data).digest()
                
                payload_match = actual_payload_hash == stored_payload_hash
                key_match = actual_key_hash == stored_key_hash
                
                print(f"  Payload哈希匹配: {payload_match}")
                print(f"  密钥哈希匹配: {key_match}")
                
                if not payload_match and not key_match:
                    print(f"  ✅ Type 1绕过条件满足")
                else:
                    print(f"  ⚠️  哈希匹配，不是绕过")
            else:
                print(f"  ⚠️  不是Type 1证书")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")

if __name__ == "__main__":
    comprehensive_test()
