#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
理解密钥哈希计算
基于您的payload哈希脚本和IDA分析
"""

import struct
import hashlib
import sys

def analyze_payload_hash_flow(filename):
    """分析payload哈希流程，对应您的脚本"""
    print(f"=== 分析Payload哈希流程 (基于您的脚本) ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 对应您脚本的第19行
    payload_size = struct.unpack('<I', data[48:52])[0]
    print(f"1. payload_size = data[48:52] = 0x{payload_size:x}")
    
    # 对应您脚本的第23-24行
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    print(f"2. cert_offset_pos = payload_size + 552 = 0x{cert_offset_pos:x}")
    print(f"3. cert_offset = data[cert_offset_pos:cert_offset_pos + 8] = 0x{cert_offset:x}")
    
    # 对应您脚本的第31-32行
    hash_start = 0x200
    hash_length = payload_size
    print(f"4. hash_start = 0x200")
    print(f"5. hash_length = payload_size = 0x{hash_length:x}")
    
    # 对应您脚本的第37行
    calculated_hash = hashlib.sha256(data[hash_start:hash_start + hash_length]).digest()
    print(f"6. calculated_hash = SHA256(data[0x200:0x200 + payload_size])")
    print(f"   结果: {calculated_hash.hex()}")
    
    # 对应您脚本的第40行
    expected_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    print(f"7. expected_hash = data[cert_offset + 268:cert_offset + 268 + 32]")
    print(f"   结果: {expected_hash.hex()}")
    
    print(f"8. 匹配: {'YES' if calculated_hash == expected_hash else 'NO'}")
    
    return calculated_hash == expected_hash

def analyze_key_hash_flow_from_ida(filename):
    """基于IDA分析密钥哈希流程"""
    print(f"\n=== 分析密钥哈希流程 (基于IDA) ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  payload_size = 0x{payload_size:x}")
    print(f"  cert_offset = 0x{cert_offset:x}")
    
    # 现在分析RSA验证函数中的密钥哈希计算
    print(f"\n分析RSA验证函数中的密钥哈希计算:")
    
    # 查看rsa_signature_verify函数的汇编代码
    # 根据之前的分析，密钥哈希计算在0x9f009e80附近
    print(f"根据汇编代码 0x9f009e80:")
    print(f"  ADD X0, X19, #4        ; X0 = cert_address + 4")
    print(f"  MOV W1, #0x108         ; W1 = 264")
    print(f"  BL sha256_hash_wrapper ; 调用SHA256")
    
    # 所以密钥哈希的计算应该是:
    key_hash_start = cert_offset + 4
    key_hash_length = 0x108  # 264
    
    print(f"\n密钥哈希计算参数:")
    print(f"  key_hash_start = cert_offset + 4 = 0x{key_hash_start:x}")
    print(f"  key_hash_length = 264 = 0x{key_hash_length:x}")
    
    # 计算密钥哈希
    if key_hash_start + key_hash_length <= len(data):
        key_data = data[key_hash_start:key_hash_start + key_hash_length]
        calculated_key_hash = hashlib.sha256(key_data).digest()
        
        print(f"  key_data前16字节: {key_data[:16].hex()}")
        print(f"  key_data后16字节: {key_data[-16:].hex()}")
        print(f"  calculated_key_hash: {calculated_key_hash.hex()}")
        
        # 获取存储的密钥哈希
        stored_key_hash_pos = cert_offset + 300
        stored_key_hash = data[stored_key_hash_pos:stored_key_hash_pos + 32]
        print(f"  stored_key_hash: {stored_key_hash.hex()}")
        
        print(f"  匹配: {'YES' if calculated_key_hash == stored_key_hash else 'NO'}")
        
        return calculated_key_hash == stored_key_hash
    else:
        print(f"  ❌ 密钥哈希计算范围超出文件")
        return False

def debug_certificate_structure(filename):
    """调试证书结构"""
    print(f"\n=== 调试证书结构 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书结构分析 (起始: 0x{cert_offset:x}):")
    
    # 解析证书头部
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"  +0x000: 证书类型 = {cert_type}")
    print(f"  +0x004: 密钥位长 = 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"  +0x008: e值 = 0x{e_value:x}")
    
    # 显示关键偏移的数据
    offsets_to_check = [0, 4, 8, 12, 268, 300, 332]
    for offset in offsets_to_check:
        pos = cert_offset + offset
        if pos + 16 <= len(data):
            data_chunk = data[pos:pos + 16]
            print(f"  +0x{offset:03x}: {data_chunk.hex()}")
    
    # 特别检查密钥哈希区域
    key_hash_pos = cert_offset + 300
    key_hash = data[key_hash_pos:key_hash_pos + 32]
    print(f"\n密钥哈希 (cert+300): {key_hash.hex()}")
    
    return True

def main():
    if len(sys.argv) != 2:
        print("用法: python understand_key_hash_calculation.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("理解密钥哈希计算")
    print("="*50)
    
    # 1. 分析payload哈希流程 (基于您的脚本)
    payload_success = analyze_payload_hash_flow(filename)
    
    # 2. 分析密钥哈希流程 (基于IDA)
    key_success = analyze_key_hash_flow_from_ida(filename)
    
    # 3. 调试证书结构
    debug_certificate_structure(filename)
    
    print(f"\n{'='*50}")
    print(f"Payload哈希验证: {'✅ 通过' if payload_success else '❌ 失败'}")
    print(f"密钥哈希验证: {'✅ 通过' if key_success else '❌ 失败'}")
    
    if not key_success:
        print("\n需要进一步分析密钥哈希计算的细节...")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
