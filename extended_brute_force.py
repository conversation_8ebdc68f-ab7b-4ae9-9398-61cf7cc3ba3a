#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展暴力匹配 - 尝试更多算法和数据源
"""

import struct
import hashlib
import sys
import os

def extended_brute_force(filename):
    """扩展暴力匹配"""
    print(f"=== 扩展暴力匹配 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    target_part1 = bytes.fromhex('2ffda7503f684a313b81395f6da5cc59')
    target_part2 = bytes.fromhex('3a1895cfe8658406c7d159752c47575e')
    target_full = target_part1 + target_part2
    
    print(f"目标前16字节: {target_part1.hex()}")
    print(f"目标后16字节: {target_part2.hex()}")
    
    # 尝试其他哈希算法
    print(f"\n=== 测试其他哈希算法 ===")
    
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    
    # 测试MD5
    try:
        md5_result = hashlib.md5(key_data).digest()
        if md5_result == target_part1:
            print(f"✅ MD5匹配前16字节!")
            return True
    except:
        pass
    
    # 测试SHA1
    try:
        sha1_result = hashlib.sha1(key_data).digest()
        if sha1_result[:16] == target_part1:
            print(f"✅ SHA1匹配前16字节!")
            print(f"   完整SHA1: {sha1_result.hex()}")
            return True
    except:
        pass
    
    # 测试SHA512
    try:
        sha512_result = hashlib.sha512(key_data).digest()
        if sha512_result[:16] == target_part1:
            print(f"✅ SHA512匹配前16字节!")
            print(f"   完整SHA512: {sha512_result.hex()}")
            return True
    except:
        pass
    
    # 尝试文件中的其他区域
    print(f"\n=== 搜索文件中的其他区域 ===")
    
    # 搜索整个文件，寻找可能产生目标哈希的数据
    search_ranges = [
        (0, 0x200, "文件头部"),
        (0x200, payload_size, "Payload数据"),
        (payload_size, cert_offset - payload_size, "中间区域"),
        (cert_offset + 596, len(data) - cert_offset - 596, "证书后数据"),
    ]
    
    for start, length, desc in search_ranges:
        if start + length <= len(data) and length > 0:
            # 尝试不同的子区域
            for sub_start in range(0, min(length, 1000), 16):  # 每16字节尝试一次
                for sub_len in [16, 32, 64, 128, 256, 264, 268]:
                    if sub_start + sub_len <= length:
                        test_data = data[start + sub_start:start + sub_start + sub_len]
                        hash_result = hashlib.sha256(test_data).digest()
                        
                        if hash_result[:16] == target_part1:
                            print(f"✅ 前16字节匹配: {desc} +{sub_start}, {sub_len}字节")
                            print(f"   偏移: 0x{start + sub_start:x}")
                            print(f"   完整哈希: {hash_result.hex()}")
                            return True
    
    # 尝试XOR操作
    print(f"\n=== 测试XOR操作 ===")
    
    # 与payload哈希XOR
    payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    # 密钥数据哈希与payload哈希XOR
    key_hash = hashlib.sha256(key_data).digest()
    xor_result = bytes(a ^ b for a, b in zip(key_hash, payload_hash))
    
    if xor_result[:16] == target_part1:
        print(f"✅ XOR匹配前16字节: 密钥哈希 XOR payload哈希")
        print(f"   XOR结果: {xor_result.hex()}")
        return True
    
    # 尝试简单的数学运算
    print(f"\n=== 测试数学运算 ===")
    
    # 尝试将目标值作为数字进行运算
    target_int = int.from_bytes(target_part1, 'little')
    
    # 检查是否是某些已知值的运算结果
    known_values = [
        payload_size,
        cert_offset,
        len(data),
        int.from_bytes(key_hash[:16], 'little'),
        int.from_bytes(payload_hash[:16], 'little'),
    ]
    
    for val in known_values:
        # 加法
        if (val + payload_size) & 0xffffffffffffffffffffffffffffffff == target_int:
            print(f"✅ 数学匹配: {val} + {payload_size}")
            return True
        
        # 异或
        if (val ^ payload_size) & 0xffffffffffffffffffffffffffffffff == target_int:
            print(f"✅ 数学匹配: {val} XOR {payload_size}")
            return True
    
    # 尝试查找原始数据
    print(f"\n=== 搜索原始数据存在 ===")
    
    # 搜索目标值是否直接存在于文件中的其他位置
    for i in range(len(data) - 15):
        if data[i:i + 16] == target_part1:
            print(f"✅ 找到前16字节原始数据在偏移: 0x{i:x}")
            
            # 检查周围的数据
            context_start = max(0, i - 64)
            context_end = min(len(data), i + 80)
            context = data[context_start:context_end]
            
            print(f"   上下文数据:")
            for j in range(0, len(context), 16):
                chunk = context[j:j + 16]
                offset = context_start + j
                print(f"   0x{offset:x}: {chunk.hex()}")
            
            # 尝试对周围数据进行哈希
            for ctx_start in range(max(0, i - 256), i, 16):
                for ctx_len in [16, 32, 64, 128, 256, 264]:
                    if ctx_start + ctx_len <= len(data):
                        ctx_data = data[ctx_start:ctx_start + ctx_len]
                        ctx_hash = hashlib.sha256(ctx_data).digest()
                        
                        if ctx_hash[:16] == target_part1:
                            print(f"   ✅ 上下文哈希匹配: 0x{ctx_start:x}, {ctx_len}字节")
                            return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python extended_brute_force.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("扩展暴力匹配hash key计算方法")
    print("="*60)
    
    found = extended_brute_force(filename)
    
    print(f"\n{'='*60}")
    if found:
        print("🎉 找到了匹配的方法！")
    else:
        print("❌ 扩展搜索也未找到匹配")
        print("这个hash key可能是:")
        print("  1. 使用了专有算法")
        print("  2. 来自外部密钥材料")
        print("  3. 预先计算的固定值")
        print("  4. 需要特殊的密码学库")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
