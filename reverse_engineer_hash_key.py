#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逆向工程hash key计算方法
目标：找到能计算出 2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e 的方法
"""

import struct
import hashlib
import sys

def reverse_engineer_hash_key(filename):
    """逆向工程hash key的计算方法"""
    print(f"=== 逆向工程hash key计算 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 目标hash key
    target_hash = bytes.fromhex("2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e")
    print(f"目标hash key: {target_hash.hex()}")
    
    # 基本信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 方法1: 测试证书的不同区域和长度组合
    print(f"\n=== 测试证书的不同区域 ===")
    
    # 从证书开始的不同偏移和长度
    test_ranges = [
        (0, 264),      # 从证书开始
        (4, 264),      # 跳过类型字段
        (8, 260),      # 跳过类型和密钥长度
        (12, 256),     # 只有模数
        (0, 268),      # 包含数据哈希
        (0, 300),      # 到hash key之前
        (4, 296),      # 跳过类型，到hash key之前
        (0, 332),      # 包含hash key
        (332, 256),    # 从签名开始
        (0, 588),      # 整个证书
    ]
    
    for start_offset, length in test_ranges:
        start_pos = cert_offset + start_offset
        if start_pos + length <= len(data):
            test_data = data[start_pos:start_pos + length]
            test_hash = hashlib.sha256(test_data).digest()
            match = test_hash == target_hash
            print(f"证书+{start_offset:3d}, 长度{length:3d}: {'MATCH!' if match else 'NO'}")
            if match:
                print(f"🎉 找到匹配！数据范围: 0x{start_pos:x} - 0x{start_pos + length:x}")
                return True
    
    # 方法2: 测试文件的其他区域
    print(f"\n=== 测试文件的其他区域 ===")
    
    # 测试payload区域
    payload_start = 0x200
    payload_data = data[payload_start:payload_start + payload_size]
    payload_hash = hashlib.sha256(payload_data).digest()
    print(f"Payload区域: {'MATCH!' if payload_hash == target_hash else 'NO'}")
    
    # 测试DHTB头部
    header_data = data[0:0x200]
    header_hash = hashlib.sha256(header_data).digest()
    print(f"DHTB头部: {'MATCH!' if header_hash == target_hash else 'NO'}")
    
    # 测试整个文件
    file_hash = hashlib.sha256(data).digest()
    print(f"整个文件: {'MATCH!' if file_hash == target_hash else 'NO'}")
    
    # 方法3: 测试组合数据
    print(f"\n=== 测试组合数据 ===")
    
    # payload + 证书
    combo1 = payload_data + data[cert_offset:cert_offset + 264]
    combo1_hash = hashlib.sha256(combo1).digest()
    print(f"Payload+证书: {'MATCH!' if combo1_hash == target_hash else 'NO'}")
    
    # 头部 + payload
    combo2 = header_data + payload_data
    combo2_hash = hashlib.sha256(combo2).digest()
    print(f"头部+Payload: {'MATCH!' if combo2_hash == target_hash else 'NO'}")
    
    # 方法4: 测试特殊的数据处理
    print(f"\n=== 测试特殊数据处理 ===")
    
    # 也许需要字节序转换
    cert_data = data[cert_offset + 4:cert_offset + 4 + 264]
    
    # 转换密钥长度和e值为大端序
    modified_data = bytearray(cert_data)
    key_len = struct.unpack('<I', modified_data[0:4])[0]
    e_val = struct.unpack('<I', modified_data[4:8])[0]
    modified_data[0:4] = struct.pack('>I', key_len)
    modified_data[4:8] = struct.pack('>I', e_val)
    
    modified_hash = hashlib.sha256(modified_data).digest()
    print(f"字节序转换: {'MATCH!' if modified_hash == target_hash else 'NO'}")
    
    # 方法5: 暴力搜索文件中的所有256字节区域
    print(f"\n=== 暴力搜索256字节区域 ===")
    
    found_match = False
    for start in range(0, len(data) - 256, 4):  # 每4字节对齐
        test_data = data[start:start + 256]
        test_hash = hashlib.sha256(test_data).digest()
        if test_hash == target_hash:
            print(f"🎉 找到匹配！位置: 0x{start:x} - 0x{start + 256:x}")
            print(f"数据开头: {test_data[:32].hex()}")
            found_match = True
            break
    
    if not found_match:
        print("256字节搜索未找到匹配")
    
    # 方法6: 搜索其他常见长度
    print(f"\n=== 搜索其他长度 ===")
    
    common_lengths = [32, 64, 128, 264, 268, 300, 332, 512, 1024]
    
    for length in common_lengths:
        found = False
        for start in range(0, min(len(data) - length, 0x90000), 16):  # 限制搜索范围
            test_data = data[start:start + length]
            test_hash = hashlib.sha256(test_data).digest()
            if test_hash == target_hash:
                print(f"🎉 长度{length}匹配！位置: 0x{start:x}")
                found = True
                break
        if not found:
            print(f"长度{length}: 未找到")
    
    return False

def analyze_hash_key_context(filename):
    """分析hash key在文件中的上下文"""
    print(f"\n=== 分析hash key上下文 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    target_hash = bytes.fromhex("2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e")
    
    # 在文件中查找这个hash key
    hash_pos = data.find(target_hash)
    if hash_pos != -1:
        print(f"Hash key在文件中的位置: 0x{hash_pos:x}")
        
        # 显示周围的数据
        context_start = max(0, hash_pos - 64)
        context_end = min(len(data), hash_pos + 32 + 64)
        context_data = data[context_start:context_end]
        
        print(f"上下文数据 (0x{context_start:x} - 0x{context_end:x}):")
        for i in range(0, len(context_data), 16):
            chunk = context_data[i:i+16]
            offset = context_start + i
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            marker = ' <-- HASH KEY' if offset <= hash_pos < offset + 16 else ''
            print(f"0x{offset:08x}: {hex_str:<48} {marker}")
    else:
        print("Hash key未在文件中找到")

def main():
    if len(sys.argv) != 2:
        print("用法: python reverse_engineer_hash_key.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("逆向工程hash key计算方法")
    print("="*50)
    
    # 分析hash key的上下文
    analyze_hash_key_context(filename)
    
    # 逆向工程计算方法
    success = reverse_engineer_hash_key(filename)
    
    print(f"\n{'='*50}")
    if success:
        print("✅ 找到hash key的计算方法！")
    else:
        print("❌ 未找到hash key的计算方法")
        print("可能需要更复杂的数据处理或组合")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
