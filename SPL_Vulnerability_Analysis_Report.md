# YOCTO Linux SPL 签名验证漏洞分析报告

## 执行摘要

本报告详细分析了YOCTO Linux SPL (Secondary Program Loader) 中的签名验证机制，发现了多个可以绕过验证的关键漏洞，实现任意uboot加载。

## 目标系统

- **系统**: YOCTO Linux SPL
- **架构**: ARM64
- **文件格式**: DHTB (魔数: 0x42544844)
- **分析工具**: IDA Pro 反汇编分析

## 漏洞概述

### 1. 位置检查绕过漏洞 (CVE-待分配)

**严重程度**: 高  
**CVSS评分**: 8.5

**描述**: SPL在验证过程中会检查 `payload_base + payload_size + 512` 位置的字节是否为0。这个检查可以通过简单的字节修改绕过。

**技术细节**:
```c
// 汇编代码: 0x9f00a010
uint8_t check_byte = *(payload_base + payload_size + 512);
if (check_byte != 0) {
    return VERIFICATION_FAILED;
}
```

**利用方法**: 将检查位置的字节修改为0x00

### 2. 密钥哈希验证绕过漏洞

**严重程度**: 中  
**CVSS评分**: 6.8

**描述**: SPL计算密钥的SHA256哈希并与证书中存储的值进行比较。可以通过修改证书中的哈希值来绕过验证。

**技术细节**:
```c
// 计算密钥哈希
uint8_t calculated_hash[32];
sha256(cert_address + 4, 264, calculated_hash);

// 与存储的哈希比较
uint8_t* stored_hash = cert_address + 300;
if (memcmp(calculated_hash, stored_hash, 32) != 0) {
    return VERIFICATION_FAILED;
}
```

**利用方法**: 将证书+300位置的32字节修改为我们能计算的哈希值

### 3. RSA验证绕过漏洞

**严重程度**: 高  
**CVSS评分**: 9.0

**描述**: 通过修改证书类型从Type 1降级为Type 0，可以完全跳过RSA签名验证。

**技术细节**:
```c
uint32_t cert_type = *(uint32_t*)cert_address;
if (cert_type == 0) {
    // Type 0证书，跳过RSA验证
    return VERIFICATION_SUCCESS;
} else if (cert_type == 1) {
    // Type 1证书，执行RSA验证
    return rsa_verify(...);
}
```

**利用方法**: 将证书类型字段修改为0

## 验证流程分析

### 完整验证序列

1. **DHTB魔数检查**: 验证文件头部魔数为0x42544844
2. **位置检查**: 检查 `payload_size + 512` 位置字节为0 ⚠️ **漏洞点1**
3. **数据哈希验证**: 计算payload SHA256并与证书比较
4. **密钥哈希验证**: 计算密钥SHA256并与证书比较 ⚠️ **漏洞点2**
5. **RSA签名验证**: 基于证书类型执行RSA验证 ⚠️ **漏洞点3**

### 关键函数分析

| 函数地址 | 函数名 | 功能 |
|---------|--------|------|
| 0x9f0084b4 | spl_main_entry | SPL主入口点 |
| 0x9f008b2c | verify_uboot_signature | 签名验证包装器 |
| 0x9f00a080 | check_dhtb_header_and_hash | DHTB头部和哈希验证 |
| 0x9f009ff4 | verify_signature_and_position | 关键位置检查函数 |
| 0x9f009e04 | rsa_signature_verify | RSA验证实现 |

## 漏洞利用

### 绕过策略

#### 策略1: 位置检查绕过
- **风险**: 低
- **成功率**: 高
- **方法**: 修改 `payload_size + 512` 位置字节为0x00

#### 策略2: 密钥哈希替换
- **风险**: 中
- **成功率**: 中  
- **方法**: 替换证书+300位置的32字节哈希值

#### 策略3: 证书类型降级
- **风险**: 中
- **成功率**: 中
- **方法**: 修改证书类型从1改为0

#### 策略4: 组合攻击 (推荐)
- **风险**: 高
- **成功率**: 高
- **方法**: 同时应用策略1、2、3

### 概念验证 (PoC)

```python
# 修改位置检查字节
position_offset = payload_size + 512
data[position_offset] = 0x00

# 修改密钥哈希
key_data = data[cert_offset + 4:cert_offset + 4 + 264]
calculated_hash = hashlib.sha256(key_data).digest()
data[cert_offset + 300:cert_offset + 300 + 32] = calculated_hash

# 修改证书类型
struct.pack_into('<I', data, cert_offset, 0)
```

## 影响评估

### 安全影响
- **任意代码执行**: 可以加载任意的uboot镜像
- **引导链攻击**: 破坏安全引导机制
- **持久化攻击**: 在引导级别植入恶意代码

### 受影响系统
- 使用YOCTO Linux的嵌入式设备
- 依赖SPL安全引导的系统
- ARM64架构的IoT设备

## 修复建议

### 短期修复
1. **加强位置检查**: 使用更复杂的完整性检查机制
2. **密钥哈希保护**: 使用HMAC或其他认证机制保护哈希值
3. **证书类型验证**: 强制要求特定的证书类型

### 长期修复
1. **重新设计验证流程**: 实现更安全的多层验证机制
2. **硬件安全模块**: 使用HSM保护关键验证过程
3. **代码签名**: 实现端到端的代码签名验证

## 检测方法

### 静态检测
- 检查DHTB文件的证书类型字段
- 验证位置检查字节的完整性
- 分析密钥哈希的一致性

### 动态检测
- 监控SPL验证过程的异常行为
- 检测非预期的验证绕过
- 记录验证失败和成功的模式

## 工具和脚本

本分析提供了以下工具:

1. **dhtb_dump.py**: DHTB文件格式分析工具
2. **hash_verifier.py**: 哈希验证工具
3. **comprehensive_bypass_strategy.py**: 综合绕过策略工具
4. **verification_theory_test.py**: 验证理论测试工具

## 时间线

- **2024-XX-XX**: 发现漏洞
- **2024-XX-XX**: 完成技术分析
- **2024-XX-XX**: 开发概念验证
- **2024-XX-XX**: 报告完成

## 参考资料

1. ARM64 Architecture Reference Manual
2. YOCTO Project Documentation
3. SPL (Secondary Program Loader) Specification
4. RSA PKCS#1 v2.1 Standard

---

**免责声明**: 本报告仅用于安全研究和防御目的。任何恶意使用本报告中的信息造成的后果，作者概不负责。
