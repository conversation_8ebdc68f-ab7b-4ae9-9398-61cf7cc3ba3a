#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Type 1哈希绕过漏洞测试脚本
"""

import struct
import hashlib

def test_type1_bypass(filename):
    """测试Type 1绕过"""
    print(f"测试文件: {filename}")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 解析结构
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"证书类型: {cert_type}")
    
    # 验证哈希不匹配
    payload_data = data[0x200:0x200 + payload_size]
    stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    actual_payload_hash = hashlib.sha256(payload_data).digest()
    
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    actual_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"Payload哈希匹配: {actual_payload_hash == stored_payload_hash}")
    print(f"密钥哈希匹配: {actual_key_hash == stored_key_hash}")
    
    if cert_type == 1 and not (actual_payload_hash == stored_payload_hash and actual_key_hash == stored_key_hash):
        print("✅ Type 1哈希绕过测试成功！")
        return True
    else:
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    test_type1_bypass("uboot3_type1_hash_bypass_exploit")
