#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密钥哈希计算工具
基于SPL Loader的RSA验证逻辑计算密钥哈希
"""

import struct
import hashlib
import sys
import os

def calculate_key_hash(filename):
    """计算uboot文件中的密钥哈希"""
    print(f"=== 分析文件: {filename} ===")
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"文件读取错误: {e}")
        return None
    
    # 基本信息
    file_size = len(data)
    magic = struct.unpack('<I', data[:4])[0]
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    print(f"文件大小: 0x{file_size:x}")
    print(f"DHTB魔数: 0x{magic:08x} ({'PASS' if magic == 0x42544844 else 'FAIL'})")
    print(f"Payload大小: 0x{payload_size:x}")
    
    # 获取证书信息
    cert_size_offset = payload_size + 544
    cert_offset_offset = payload_size + 552
    
    if cert_offset_offset + 8 > file_size:
        print("证书偏移超出文件范围")
        return None
    
    cert_size = struct.unpack('<Q', data[cert_size_offset:cert_size_offset + 8])[0]
    cert_offset = struct.unpack('<Q', data[cert_offset_offset:cert_offset_offset + 8])[0]
    
    print(f"证书大小: 0x{cert_size:x}")
    print(f"证书偏移: 0x{cert_offset:x}")
    
    if cert_offset + 340 > file_size:
        print("证书数据超出文件范围")
        return None
    
    # 读取证书类型
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"证书类型: 0x{cert_type:x}")
    
    # 读取密钥信息
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"密钥位长度: 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"e值: 0x{e_value:x}")
    
    # 计算密钥哈希
    # 根据SPL代码：sha256_hash_wrapper(cert_address + 4, 0x108, hash_buffer)
    # 0x108 = 264 = 4(key_bit_len) + 4(e) + 256(modulus)
    key_data_start = cert_offset + 4  # cert_address + 4
    key_data_length = 0x108  # 264字节
    
    if key_data_start + key_data_length > file_size:
        print(f"密钥数据超出文件范围: 0x{key_data_start:x} + 0x{key_data_length:x} > 0x{file_size:x}")
        return None
    
    key_data = data[key_data_start:key_data_start + key_data_length]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"\n=== 密钥哈希计算 ===")
    print(f"密钥数据范围: 0x{key_data_start:x} - 0x{key_data_start + key_data_length:x}")
    print(f"密钥数据长度: 0x{key_data_length:x} ({key_data_length} bytes)")
    print(f"计算的密钥哈希: {calculated_key_hash.hex()}")
    
    # 读取证书中存储的哈希值进行比较
    if cert_type == 1:
        # Type 1证书：密钥哈希在offset + 300
        stored_key_hash = data[cert_offset + 300:cert_offset + 300 + 32]
        print(f"证书中的密钥哈希: {stored_key_hash.hex()}")
        print(f"密钥哈希验证: {'PASS' if calculated_key_hash == stored_key_hash else 'FAIL'}")
    else:
        print("Type 0证书不包含密钥哈希字段")
    
    # 读取数据哈希
    data_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    print(f"证书中的数据哈希: {data_hash.hex()}")
    
    return {
        'key_hash': calculated_key_hash,
        'data_hash': data_hash,
        'cert_type': cert_type,
        'cert_offset': cert_offset,
        'key_data': key_data
    }

def verify_payload_hash(filename):
    """验证payload哈希计算"""
    print(f"\n=== Payload哈希验证 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    # 计算payload哈希 (从0x200开始，长度为payload_size)
    hash_start = 0x200
    hash_length = payload_size
    
    if hash_start + hash_length > len(data):
        print(f"哈希计算范围超出文件大小")
        return None
    
    calculated_payload_hash = hashlib.sha256(data[hash_start:hash_start + hash_length]).digest()
    print(f"Payload哈希范围: 0x{hash_start:x} - 0x{hash_start + hash_length:x}")
    print(f"计算的Payload哈希: {calculated_payload_hash.hex()}")
    
    return calculated_payload_hash

def create_custom_key_hash(modulus_hex, e_value=0x1000100, key_bit_len=0x800):
    """为自定义密钥创建密钥哈希"""
    print(f"\n=== 创建自定义密钥哈希 ===")
    
    # 构造密钥数据结构 (与证书格式相同)
    # 4字节密钥位长度 + 4字节e值 + 256字节模数
    key_data = struct.pack('<I', key_bit_len)  # 密钥位长度
    key_data += struct.pack('<I', e_value)     # e值
    
    # 解析模数
    if isinstance(modulus_hex, str):
        # 移除空格和换行
        modulus_hex = modulus_hex.replace(' ', '').replace('\n', '')
        modulus_bytes = bytes.fromhex(modulus_hex)
    else:
        modulus_bytes = modulus_hex
    
    # 确保模数是256字节
    if len(modulus_bytes) < 256:
        modulus_bytes = modulus_bytes + b'\x00' * (256 - len(modulus_bytes))
    elif len(modulus_bytes) > 256:
        modulus_bytes = modulus_bytes[:256]
    
    key_data += modulus_bytes
    
    # 计算哈希
    key_hash = hashlib.sha256(key_data).digest()
    
    print(f"密钥位长度: 0x{key_bit_len:x}")
    print(f"e值: 0x{e_value:x}")
    print(f"模数长度: {len(modulus_bytes)} bytes")
    print(f"计算的密钥哈希: {key_hash.hex()}")
    
    return key_hash

def analyze_verification_chain(filename):
    """分析完整的验证链"""
    print(f"\n{'='*50}")
    print(f"完整验证链分析: {filename}")
    print(f"{'='*50}")

    # 1. 计算密钥哈希
    key_info = calculate_key_hash(filename)
    if not key_info:
        return False

    # 2. 验证payload哈希
    payload_hash = verify_payload_hash(filename)
    if not payload_hash:
        return False

    # 3. 正确的验证逻辑分析
    print(f"\n=== SPL验证逻辑分析 ===")
    cert_offset = key_info['cert_offset']
    with open(filename, 'rb') as f:
        data = f.read()

    # SPL从cert+300读取哈希值作为"预期的密钥哈希"
    expected_key_hash_from_cert = data[cert_offset + 300:cert_offset + 300 + 32]
    print(f"从证书+300读取的哈希: {expected_key_hash_from_cert.hex()}")

    # SPL计算实际的密钥哈希
    calculated_key_hash = key_info['key_hash']
    print(f"计算的密钥哈希:     {calculated_key_hash.hex()}")

    # 比较结果
    key_hash_match = calculated_key_hash == expected_key_hash_from_cert
    print(f"密钥哈希验证: {'PASS' if key_hash_match else 'FAIL'}")

    # 数据哈希验证
    data_hash_match = payload_hash == key_info['data_hash']
    print(f"数据哈希验证: {'PASS' if data_hash_match else 'FAIL'}")

    # 4. 分析绕过机会
    print(f"\n=== 绕过机会分析 ===")
    if not key_hash_match:
        print("🔥 密钥哈希不匹配 - 可能的绕过点！")
        print("   可以尝试：")
        print("   1. 修改证书中offset+300的密钥哈希字段")
        print("   2. 构造匹配的密钥数据")
    else:
        print("✅ 密钥哈希验证通过")

    if not data_hash_match:
        print("🔥 数据哈希不匹配 - 可能的绕过点！")
        print("   可以尝试：")
        print("   1. 修改payload数据使哈希匹配")
        print("   2. 修改证书中的数据哈希字段")
    else:
        print("✅ 数据哈希验证通过")

    return True

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  python key_hash_calculator.py <uboot文件>")
        print("  python key_hash_calculator.py uboot")
        return 1
    
    filename = sys.argv[1]
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return 1
    
    # 执行完整分析
    success = analyze_verification_chain(filename)
    
    if success:
        print(f"\n✅ 分析完成！")
    else:
        print(f"\n❌ 分析失败")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
