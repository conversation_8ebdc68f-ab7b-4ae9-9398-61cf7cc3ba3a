#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度验证逻辑分析
重新仔细分析每个步骤，找出正确的密钥哈希计算方法
"""

import struct
import hashlib
import sys

def analyze_rsa_function_parameters(filename):
    """分析RSA函数的参数传递"""
    print(f"=== RSA函数参数传递分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print("基于汇编代码分析RSA验证函数的调用:")
    print()
    
    # 从verify_signature_and_position函数的调用
    print("verify_signature_and_position函数调用RSA验证:")
    print("参数准备:")
    print("- X0: 第一个参数 (可能是payload哈希)")
    print("- X1: 第二个参数 (可能是数据哈希)")  
    print("- X2: 第三个参数 (证书地址)")
    
    # 计算实际的哈希值
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    data_hash_cert = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"\n实际值:")
    print(f"Payload哈希: {payload_hash.hex()}")
    print(f"证书数据哈希: {data_hash_cert.hex()}")
    print(f"匹配: {'YES' if payload_hash == data_hash_cert else 'NO'}")
    
    return payload_hash, data_hash_cert

def analyze_key_hash_calculation_detailed(filename):
    """详细分析密钥哈希计算"""
    print(f"\n=== 详细密钥哈希计算分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 目标哈希值
    target_hash = data[cert_offset + 300:cert_offset + 300 + 32]
    print(f"目标密钥哈希: {target_hash.hex()}")
    
    print(f"\n汇编代码逐步执行模拟:")
    
    # 步骤1: 准备参数
    print(f"1. 准备SHA256计算参数:")
    cert_addr = cert_offset
    data_start = cert_addr + 4
    data_length = 0x108  # 264
    
    print(f"   证书地址: 0x{cert_addr:x}")
    print(f"   数据起始: 0x{data_start:x} (cert + 4)")
    print(f"   数据长度: {data_length} bytes")
    
    # 步骤2: 读取数据
    key_data = data[data_start:data_start + data_length]
    print(f"2. 读取密钥数据:")
    print(f"   前16字节: {key_data[:16].hex()}")
    print(f"   后16字节: {key_data[-16:].hex()}")
    
    # 步骤3: 计算SHA256
    calculated_hash = hashlib.sha256(key_data).digest()
    print(f"3. 计算SHA256:")
    print(f"   计算结果: {calculated_hash.hex()}")
    print(f"   目标值:   {target_hash.hex()}")
    print(f"   匹配:     {'YES' if calculated_hash == target_hash else 'NO'}")
    
    if calculated_hash != target_hash:
        print(f"\n❌ 标准计算不匹配，尝试其他方法...")
        
        # 尝试不同的数据范围
        test_methods = [
            ("cert+0, 264字节", data[cert_addr:cert_addr + 264]),
            ("cert+4, 260字节", data[cert_addr + 4:cert_addr + 4 + 260]),
            ("cert+8, 256字节", data[cert_addr + 8:cert_addr + 8 + 256]),
            ("cert+12, 252字节", data[cert_addr + 12:cert_addr + 12 + 252]),
            ("cert+4, 268字节", data[cert_addr + 4:cert_addr + 4 + 268]),
            ("cert+0, 268字节", data[cert_addr:cert_addr + 268]),
        ]
        
        for method_name, test_data in test_methods:
            if len(test_data) > 0:
                test_hash = hashlib.sha256(test_data).digest()
                match = test_hash == target_hash
                print(f"   {method_name}: {'MATCH!' if match else 'NO'}")
                if match:
                    return test_data, test_hash
    
    return key_data, calculated_hash

def analyze_memory_layout(filename):
    """分析内存布局和数据结构"""
    print(f"\n=== 内存布局分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书结构详细分析 (起始: 0x{cert_offset:x}):")
    
    # 解析证书结构
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"  +0x000: 证书类型 = {cert_type}")
    print(f"  +0x004: 密钥位长 = 0x{key_bit_len:x} ({key_bit_len} bits)")
    print(f"  +0x008: e值 = 0x{e_value:x}")
    print(f"  +0x00C: 模数开始")
    
    # 显示模数
    modulus = data[cert_offset + 12:cert_offset + 12 + 256]
    print(f"  模数前16字节: {modulus[:16].hex()}")
    print(f"  模数后16字节: {modulus[-16:].hex()}")
    
    # 显示哈希字段
    data_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    key_hash = data[cert_offset + 300:cert_offset + 300 + 32]
    
    print(f"  +0x10C: 数据哈希 = {data_hash.hex()}")
    print(f"  +0x12C: 密钥哈希 = {key_hash.hex()}")
    
    # 检查是否有隐藏的数据结构
    print(f"\n检查可能的数据结构变化:")
    
    # 也许密钥长度字段影响了计算范围
    if key_bit_len == 0x800:  # 2048 bits
        actual_key_bytes = key_bit_len // 8  # 256 bytes
        print(f"  基于密钥长度的实际字节数: {actual_key_bytes}")
        
        # 尝试基于实际密钥长度的计算
        test_data = data[cert_offset + 4:cert_offset + 4 + 4 + actual_key_bytes]  # key_len + e + modulus
        test_hash = hashlib.sha256(test_data).digest()
        target_hash = key_hash
        
        print(f"  基于实际长度计算: {'MATCH!' if test_hash == target_hash else 'NO'}")
        
        if test_hash == target_hash:
            return True
    
    return False

def test_endianness_and_encoding(filename):
    """测试字节序和编码问题"""
    print(f"\n=== 字节序和编码测试 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    target_hash = data[cert_offset + 300:cert_offset + 300 + 32]
    
    # 获取原始密钥数据
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    
    print("测试不同的字节序处理:")
    
    # 方法1: 转换密钥长度和e值为大端序
    modified_data1 = bytearray(key_data)
    key_len = struct.unpack('<I', modified_data1[0:4])[0]
    e_val = struct.unpack('<I', modified_data1[4:8])[0]
    modified_data1[0:4] = struct.pack('>I', key_len)
    modified_data1[4:8] = struct.pack('>I', e_val)
    
    hash1 = hashlib.sha256(modified_data1).digest()
    print(f"  方法1 (key_len+e大端): {'MATCH!' if hash1 == target_hash else 'NO'}")
    
    # 方法2: 整个模数转换字节序
    modified_data2 = bytearray(key_data)
    # 将256字节的模数按32位整数反转
    modulus_start = 8
    for i in range(modulus_start, modulus_start + 256, 4):
        if i + 4 <= len(modified_data2):
            val = struct.unpack('<I', modified_data2[i:i+4])[0]
            modified_data2[i:i+4] = struct.pack('>I', val)
    
    hash2 = hashlib.sha256(modified_data2).digest()
    print(f"  方法2 (模数大端): {'MATCH!' if hash2 == target_hash else 'NO'}")
    
    # 方法3: 所有数据大端序
    modified_data3 = bytearray(key_data)
    for i in range(0, len(modified_data3), 4):
        if i + 4 <= len(modified_data3):
            val = struct.unpack('<I', modified_data3[i:i+4])[0]
            modified_data3[i:i+4] = struct.pack('>I', val)
    
    hash3 = hashlib.sha256(modified_data3).digest()
    print(f"  方法3 (全部大端): {'MATCH!' if hash3 == target_hash else 'NO'}")
    
    # 检查是否有任何匹配
    if hash1 == target_hash:
        print(f"🎉 方法1匹配！")
        return True
    elif hash2 == target_hash:
        print(f"🎉 方法2匹配！")
        return True
    elif hash3 == target_hash:
        print(f"🎉 方法3匹配！")
        return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python deep_verification_logic_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("深度验证逻辑分析")
    print("="*50)
    
    # 分析RSA函数参数
    payload_hash, data_hash = analyze_rsa_function_parameters(filename)
    
    # 详细分析密钥哈希计算
    key_data, calculated_hash = analyze_key_hash_calculation_detailed(filename)
    
    # 分析内存布局
    layout_success = analyze_memory_layout(filename)
    
    # 测试字节序问题
    endian_success = test_endianness_and_encoding(filename)
    
    print(f"\n{'='*50}")
    if layout_success or endian_success:
        print("✅ 找到正确的密钥哈希计算方法！")
    else:
        print("❌ 仍未找到正确的计算方法")
        print("需要更深入的分析或可能存在特殊的预处理步骤")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
