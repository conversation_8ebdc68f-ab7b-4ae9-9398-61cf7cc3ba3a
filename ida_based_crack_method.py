#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于IDA反汇编分析的破解方法
根据rsa_signature_verify函数的逻辑实现破解
"""

import struct
import hashlib
import sys

def ida_based_crack_analysis(filename):
    """基于IDA分析的破解方法"""
    print(f"=== 基于IDA反汇编分析的破解方法 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"  证书类型: {cert_type}")
    
    # 根据IDA分析，验证函数的逻辑：
    # 1. 计算密钥哈希：SHA256(cert+4, 0x108字节) -> v19
    # 2. 比较：memcmp_custom(x0_0, v19, 32)
    # 其中 x0_0 是传入的期望密钥哈希值
    
    print(f"\n=== IDA分析结果 ===")
    print(f"验证函数逻辑:")
    print(f"  1. 计算: SHA256(cert+4, 0x108字节)")
    print(f"  2. 比较: 计算值 vs 传入的期望值")
    print(f"  3. 如果匹配，验证通过")
    
    # 计算实际的密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]  # 0x108 = 264字节
    calculated_hash = hashlib.sha256(key_data).digest()
    
    print(f"\n=== 计算结果 ===")
    print(f"密钥数据范围: cert+4 到 cert+{4+0x108:x} (264字节)")
    print(f"计算的密钥哈希: {calculated_hash.hex()}")
    
    # 读取存储的密钥哈希
    if cert_type == 1:
        # Type 1证书，密钥哈希在cert+0x12c
        stored_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
        hash_offset = "cert+0x12c"
    else:
        # Type 0证书，密钥哈希在cert+300
        stored_hash = data[cert_offset + 300:cert_offset + 300 + 32]
        hash_offset = "cert+300"
    
    print(f"存储的密钥哈希 ({hash_offset}): {stored_hash.hex()}")
    
    # 验证匹配
    is_match = calculated_hash == stored_hash
    print(f"\n=== 验证结果 ===")
    print(f"哈希匹配: {'✅ PASS' if is_match else '❌ FAIL'}")
    
    if is_match:
        print(f"🎉 这是官方uboot！密钥哈希验证通过")
        return True, "official"
    else:
        print(f"❌ 这不是官方uboot，或者被修改过")
        
        # 分析破解可能性
        print(f"\n=== 破解分析 ===")
        print(f"要绕过验证，需要:")
        print(f"  1. 修改存储的密钥哈希为计算值")
        print(f"  2. 或者修改密钥数据使其哈希匹配存储值")
        print(f"  3. 或者patch验证函数跳过比较")
        
        return False, "modified"

def generate_crack_patch(filename):
    """生成破解补丁"""
    print(f"\n=== 生成破解补丁 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    # 计算正确的密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    calculated_hash = hashlib.sha256(key_data).digest()
    
    # 确定哈希存储位置
    if cert_type == 1:
        hash_pos = cert_offset + 0x12c
        hash_desc = "cert+0x12c"
    else:
        hash_pos = cert_offset + 300
        hash_desc = "cert+300"
    
    print(f"破解方法1: 修改存储的密钥哈希")
    print(f"  位置: 0x{hash_pos:x} ({hash_desc})")
    print(f"  原值: {data[hash_pos:hash_pos + 32].hex()}")
    print(f"  新值: {calculated_hash.hex()}")
    
    # 创建补丁文件
    patch_data = data.copy()
    patch_data[hash_pos:hash_pos + 32] = calculated_hash
    
    patch_filename = filename + "_patched"
    with open(patch_filename, 'wb') as f:
        f.write(patch_data)
    
    print(f"  补丁文件: {patch_filename}")
    
    # 验证补丁
    print(f"\n验证补丁:")
    patched_hash = patch_data[hash_pos:hash_pos + 32]
    if patched_hash == calculated_hash:
        print(f"✅ 补丁验证成功")
        return patch_filename
    else:
        print(f"❌ 补丁验证失败")
        return None

def verify_patch(filename):
    """验证补丁是否有效"""
    print(f"\n=== 验证补丁效果 ===")
    
    is_official, status = ida_based_crack_analysis(filename)
    
    if is_official:
        print(f"🎉 补丁成功！现在验证通过")
    else:
        print(f"❌ 补丁失败，仍然验证不通过")
    
    return is_official

def main():
    if len(sys.argv) != 2:
        print("用法: python ida_based_crack_method.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("基于IDA反汇编分析的uboot破解")
    print("="*60)
    
    # 分析原文件
    is_official, status = ida_based_crack_analysis(filename)
    
    if not is_official:
        print(f"\n{'='*60}")
        print("开始生成破解补丁...")
        
        # 生成补丁
        patch_file = generate_crack_patch(filename)
        
        if patch_file:
            # 验证补丁
            verify_patch(patch_file)
    
    print(f"\n{'='*60}")
    print("分析完成")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
