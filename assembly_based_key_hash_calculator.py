#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于汇编代码分析的密钥哈希计算器
直接分析汇编指令来理解真实的计算逻辑
"""

import struct
import hashlib
import sys

def analyze_assembly_step_by_step(filename):
    """逐步分析汇编代码中的密钥哈希计算"""
    print(f"=== 基于汇编代码的密钥哈希计算分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 分析RSA验证函数中的密钥哈希计算部分
    print(f"\n=== 汇编代码逐步分析 ===")
    
    # 从汇编代码: 0x9f009e80: ADD X0, X19, #4
    # X19 = 证书地址, 所以 X0 = cert_address + 4
    hash_data_start = cert_offset + 4
    print(f"1. ADD X0, X19, #4")
    print(f"   X0 = 0x{hash_data_start:x} (cert_address + 4)")
    
    # 从汇编代码: 0x9f009e8c: MOV W1, #0x108
    # W1 = 0x108 = 264
    hash_data_length = 0x108
    print(f"2. MOV W1, #0x108")
    print(f"   W1 = {hash_data_length} (264 bytes)")
    
    # 从汇编代码: 0x9f009e94: BL sha256_hash_wrapper
    # 调用SHA256计算函数
    print(f"3. BL sha256_hash_wrapper")
    print(f"   计算SHA256(0x{hash_data_start:x}, {hash_data_length})")
    
    # 执行标准计算
    hash_data = data[hash_data_start:hash_data_start + hash_data_length]
    calculated_hash = hashlib.sha256(hash_data).digest()
    print(f"   结果: {calculated_hash.hex()}")
    
    # 但是让我们检查是否有其他分支或条件
    print(f"\n=== 检查证书类型分支 ===")
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"证书类型: {cert_type}")
    
    # 从汇编代码看，有两个分支：
    # 0x9f009e5c: CMP W22, #1
    # 0x9f009e64: B.LS loc_9F009E80  (如果 <= 1 跳转)
    # 0x9f009e90: B.NE loc_9F009F40  (如果 != 0 跳转到另一个分支)
    
    if cert_type == 0:
        print("Type 0 证书分支:")
        print("0x9f009f40: BL sha256_hash_wrapper")
        # Type 0 使用相同的计算方法
        print(f"使用相同的计算: SHA256(cert+4, 264)")
        
    elif cert_type == 1:
        print("Type 1 证书分支:")
        print("0x9f009e94: BL sha256_hash_wrapper")
        # Type 1 也使用相同的计算方法
        print(f"使用相同的计算: SHA256(cert+4, 264)")
    
    # 让我们检查是否有其他可能影响计算的因素
    print(f"\n=== 检查其他可能的计算因素 ===")
    
    # 检查证书中的密钥位长度字段
    key_bit_length = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    print(f"密钥位长度: 0x{key_bit_length:x} ({key_bit_length} bits)")
    
    # 检查e值
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    print(f"e值: 0x{e_value:x}")
    
    # 也许计算长度不是固定的264字节，而是基于密钥长度？
    if key_bit_length == 0x800:  # 2048 bits
        expected_key_bytes = 256  # 2048/8 = 256
        total_expected = 4 + 4 + expected_key_bytes  # bit_len + e + modulus = 264
        print(f"基于密钥长度的预期总字节数: {total_expected}")
        
        if total_expected == 264:
            print("✓ 计算长度与密钥长度一致")
        else:
            print(f"⚠ 计算长度可能需要调整为: {total_expected}")
    
    return calculated_hash

def test_alternative_calculations(filename):
    """测试其他可能的计算方法"""
    print(f"\n=== 测试其他可能的计算方法 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 获取payload哈希作为目标
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    print(f"目标payload哈希: {payload_hash.hex()}")
    
    # 方法1: 标准方法 (cert+4, 264字节)
    method1_data = data[cert_offset + 4:cert_offset + 4 + 264]
    method1_hash = hashlib.sha256(method1_data).digest()
    print(f"方法1 (标准): {method1_hash.hex()[:32]}... {'MATCH' if method1_hash == payload_hash else 'NO'}")
    
    # 方法2: 检查是否有字节序问题
    # 也许密钥长度和e值需要大端序处理
    method2_data = bytearray(method1_data)
    # 转换前8字节为大端序
    key_len = struct.unpack('<I', method2_data[0:4])[0]
    e_val = struct.unpack('<I', method2_data[4:8])[0]
    method2_data[0:4] = struct.pack('>I', key_len)
    method2_data[4:8] = struct.pack('>I', e_val)
    method2_hash = hashlib.sha256(method2_data).digest()
    print(f"方法2 (大端序): {method2_hash.hex()[:32]}... {'MATCH' if method2_hash == payload_hash else 'NO'}")
    
    # 方法3: 也许需要包含证书类型
    method3_data = data[cert_offset:cert_offset + 4 + 264]  # type + key_data
    method3_hash = hashlib.sha256(method3_data).digest()
    print(f"方法3 (+类型): {method3_hash.hex()[:32]}... {'MATCH' if method3_hash == payload_hash else 'NO'}")
    
    # 方法4: 也许长度不是264，而是基于实际密钥长度
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    if key_bit_len == 0x800:  # 2048 bits
        # 尝试不同的长度组合
        for extra_bytes in [0, 4, 8, 12, 16]:
            test_len = 264 + extra_bytes
            if cert_offset + 4 + test_len <= len(data):
                method4_data = data[cert_offset + 4:cert_offset + 4 + test_len]
                method4_hash = hashlib.sha256(method4_data).digest()
                match = method4_hash == payload_hash
                print(f"方法4 (长度{test_len}): {method4_hash.hex()[:32]}... {'MATCH' if match else 'NO'}")
                if match:
                    return method4_hash, test_len
    
    # 方法5: 也许需要跳过某些字段
    # 跳过密钥长度字段，只计算e值+模数
    method5_data = data[cert_offset + 8:cert_offset + 8 + 260]  # e + modulus
    method5_hash = hashlib.sha256(method5_data).digest()
    print(f"方法5 (e+模数): {method5_hash.hex()[:32]}... {'MATCH' if method5_hash == payload_hash else 'NO'}")
    
    # 方法6: 也许模数有特殊处理
    # 只计算模数部分
    method6_data = data[cert_offset + 12:cert_offset + 12 + 256]  # 只有模数
    method6_hash = hashlib.sha256(method6_data).digest()
    print(f"方法6 (仅模数): {method6_hash.hex()[:32]}... {'MATCH' if method6_hash == payload_hash else 'NO'}")
    
    return None, 0

def deep_assembly_analysis():
    """深入分析汇编代码的每个细节"""
    print(f"\n=== 深入汇编代码分析 ===")
    
    print("RSA验证函数关键汇编指令分析:")
    print()
    
    print("密钥哈希计算部分:")
    print("0x9f009e80: ADD X0, X19, #4        ; X0 = cert_address + 4")
    print("0x9f009e84: ADD X22, X19, #0x10C   ; X22 = cert_address + 268")
    print("0x9f009e88: ADD X2, SP, #var_80    ; X2 = 栈上缓冲区地址")
    print("0x9f009e8c: MOV W1, #0x108         ; W1 = 264")
    print("0x9f009e90: B.NE loc_9F009F40      ; 如果证书类型!=0跳转")
    print("0x9f009e94: BL sha256_hash_wrapper  ; 调用SHA256")
    print()
    
    print("关键观察:")
    print("1. X22 = cert_address + 0x10C (268) - 这指向证书中的数据哈希位置")
    print("2. SHA256计算: SHA256(cert_address + 4, 264)")
    print("3. 结果存储在栈上的var_80位置")
    print("4. 后续会有memcmp比较操作")
    print()
    
    print("比较操作分析:")
    print("0x9f009e98: MOV W2, W20            ; W20 = 32 (哈希长度)")
    print("0x9f009e9c: MOV X1, X22            ; X1 = cert_address + 268")
    print("0x9f009ea0: MOV X0, X24            ; X0 = 第二个参数(数据哈希)")
    print("0x9f009ea4: BL memcmp_custom       ; 比较数据哈希")
    print("0x9f009ea8: CBNZ W0, fail          ; 如果不匹配则失败")
    print()
    print("0x9f009eac: MOV W2, W20            ; W2 = 32")
    print("0x9f009eb0: ADD X1, SP, #var_80    ; X1 = 计算出的密钥哈希")
    print("0x9f009eb4: MOV X0, X23            ; X0 = 第一个参数(payload哈希)")
    print("0x9f009eb8: BL memcmp_custom       ; 比较密钥哈希")
    print("0x9f009ebc: CBNZ W0, fail          ; 如果不匹配则失败")
    print()
    
    print("结论:")
    print("- 第一次比较: 传入的数据哈希 vs 证书中的数据哈希")
    print("- 第二次比较: 传入的payload哈希 vs 计算的密钥哈希")
    print("- 密钥哈希计算: SHA256(cert_address + 4, 264)")

def main():
    if len(sys.argv) != 2:
        print("用法: python assembly_based_key_hash_calculator.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("基于汇编代码的密钥哈希计算器")
    print("="*50)
    
    # 深入分析汇编代码
    deep_assembly_analysis()
    
    # 逐步分析汇编执行
    standard_hash = analyze_assembly_step_by_step(filename)
    
    # 测试其他可能的计算方法
    alt_hash, alt_len = test_alternative_calculations(filename)
    
    print(f"\n{'='*50}")
    if alt_hash:
        print(f"✅ 找到匹配的计算方法！长度: {alt_len}")
    else:
        print("❌ 标准汇编分析未找到匹配的计算方法")
        print("可能需要更深入的分析或存在特殊的处理逻辑")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
