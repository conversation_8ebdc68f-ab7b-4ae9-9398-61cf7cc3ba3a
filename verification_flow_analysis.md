# YOCTO Linux SPL Loader 验证流程完整分析

## 概述
本文档详细分析了YOCTO Linux splloader分区的uboot签名验证流程，包括验证顺序、关键检查点和潜在的绕过机会。

## 验证流程图

```
spl_main_entry()
    ↓
    读取uboot数据地址 (0x9F010DAC)
    ↓
verify_uboot_signature() → check_dhtb_header_and_hash()
    ↓
    [检查点1] DHTB魔数验证 (0x42544844)
    ↓ (失败则返回1)
    初始化哈希缓冲区
    ↓
    计算payload SHA256哈希
    ↓
verify_signature_and_position()
    ↓
    [检查点2] 位置字节检查 ⚠️ 关键绕过点
    if (*(_BYTE *)(payload_base + payload_size + 512) != 0)
        return 0; // 验证失败
    ↓ (失败则返回0)
    获取payload数据地址
    ↓
    计算某种哈希值到v8缓冲区
    ↓
    获取证书地址
    ↓
rsa_signature_verify()
    ↓
    [检查点3] 证书类型验证 (必须 ≤ 1)
    ↓ (失败则返回0)
    根据证书类型分支:
    
    if (cert_type == 1):  // Type 1证书
        ↓
        [检查点4a] 计算密钥哈希
        ↓
        [检查点5a] 比较数据哈希 (cert+268 vs 计算值)
        ↓ (失败则返回0)
        [检查点6a] 比较密钥哈希 (计算值 vs 输入哈希)
        ↓ (失败则返回0)
        设置RSA参数 (e值, 模数等)
        ↓
        [检查点7a] RSA签名验证 (签名在cert+340)
        
    else:  // Type 0证书
        ↓
        [检查点4b] 计算密钥哈希
        ↓
        [检查点5b] 比较数据哈希 (cert+268 vs 计算值)
        ↓ (失败则返回0)
        [检查点6b] 比较密钥哈希 (计算值 vs 输入哈希)
        ↓ (失败则返回0)
        设置RSA参数 (e值, 模数等)
        ↓
        [检查点7b] RSA签名验证 (签名在cert+308)
```

## 关键验证点详细分析

### 1. DHTB魔数检查
- **位置**: `check_dhtb_header_and_hash()` 第4行
- **检查内容**: `*a2 != 1112819780` (0x42544844 "DHTB")
- **失败行为**: 立即返回1，验证失败
- **绕过难度**: 困难，魔数必须正确

### 2. 位置字节检查 ⚠️ **关键漏洞点**
- **位置**: `verify_signature_and_position()` 第7行
- **检查内容**: `*(_BYTE *)(a2 + *(unsigned int *)(a2 + 48) + 512LL)`
- **计算公式**: `payload_base + payload_size + 512`
- **失败行为**: 返回0，验证失败
- **Python对应**: `wrong_check_pos = payload_size + 512`
- **绕过机会**: 🔥 **高** - 可能通过操控payload_size绕过

### 3. 证书类型检查
- **位置**: `rsa_signature_verify()` 第32行
- **检查内容**: `v6 > 1` (证书类型必须≤1)
- **失败行为**: 返回0，验证失败
- **绕过难度**: 中等，需要有效的证书类型

### 4. 哈希比较检查
- **位置**: `rsa_signature_verify()` 第38行和第61行
- **检查内容**: 
  - 数据哈希比较: `memcmp_custom(a2, cert_address + 268, 32)`
  - 密钥哈希比较: `memcmp_custom(x0_0, v19, 32)`
- **失败行为**: 任一失败则返回0
- **绕过难度**: 困难，需要正确的哈希值

### 5. RSA签名验证
- **位置**: `rsa_signature_verify()` 第54行
- **检查内容**: `sub_9F008BE0()` RSA验证函数
- **签名位置**: 
  - Type 0: cert + 308 (0x134)
  - Type 1: cert + 340 (0x154)
- **失败行为**: 返回0，验证失败
- **绕过难度**: 极困难，需要有效的RSA签名

## 验证顺序的关键特点

### 短路验证 (Short-Circuit)
验证流程采用短路模式，任何一个检查点失败都会立即终止验证：

1. **DHTB魔数失败** → 立即返回，不执行后续检查
2. **位置字节失败** → 立即返回，不执行RSA验证
3. **证书类型失败** → 立即返回，不执行哈希比较
4. **哈希比较失败** → 立即返回，不执行RSA验证
5. **RSA验证失败** → 返回验证失败

### 验证依赖关系
- 位置检查 **不依赖** 哈希验证结果
- RSA验证 **依赖** 哈希验证通过
- 整个流程 **依赖** 前序检查全部通过

## 潜在绕过策略

### 策略1: 位置检查绕过 🔥 **最有希望**
```c
// 目标: 使这个检查返回false (字节为0)
if (*(_BYTE *)(payload_base + payload_size + 512) != 0)
```

**可能的绕过方法**:
1. **操控payload_size字段**: 修改offset 48处的payload_size值
2. **构造特殊payload**: 确保目标位置字节为0
3. **利用整数溢出**: 如果存在整数溢出漏洞

### 策略2: 哈希碰撞攻击
- 寻找SHA256碰撞
- 构造恶意payload但哈希相同

### 策略3: RSA签名伪造
- 分析RSA实现寻找填充攻击
- 寻找密钥验证绕过

## 下一步分析重点

1. **深入分析位置检查逻辑**
2. **研究payload_size字段的约束**
3. **分析RSA验证函数的具体实现**
4. **构造概念验证攻击**
