#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSA漏洞分析
重新分析证书类型1的RSA验证逻辑，寻找RSA验证中的bug
"""

import struct
import hashlib
import sys

def analyze_rsa_verification_logic():
    """分析RSA验证逻辑"""
    print(f"=== RSA验证逻辑分析 ===")
    
    print(f"\n🔍 重新分析IDA代码中的RSA验证:")
    
    print(f"\n1. rsa_signature_verify函数 [0x9f009e04]:")
    print(f"   第31行: if ( v6 > 1 ) return 0LL;")
    print(f"   第34行: if ( v6 == 1 )")
    print(f"   - v6 = *cert_address (证书类型)")
    print(f"   - 如果证书类型 > 1，直接返回失败")
    print(f"   - 如果证书类型 == 1，执行Type 1验证逻辑")
    print(f"   - 否则执行Type 0验证逻辑")
    
    print(f"\n2. Type 1验证逻辑 (第34-54行):")
    print(f"   第36行: sha256_hash_wrapper(cert+4, 0x108u, v19)")
    print(f"   第37行: memcmp_custom(a2, cert+268, 32)")
    print(f"   第38行: memcmp_custom(x0_0, v19, 32)")
    print(f"   第44行: sha256_hash_wrapper(cert+268, 0x48u, v28)")
    print(f"   第52行: rsa_verify_core(...)")
    
    print(f"\n3. Type 0验证逻辑 (第55-72行):")
    print(f"   第55行: sha256_hash_wrapper(cert+4, 0x108u, v19)")
    print(f"   第56行: if ( !memcmp_custom(a2, cert+268, 32) )")
    print(f"   第58行: memcmp_custom(x0_0, v19, 32)")
    print(f"   第64行: sha256_hash_wrapper(cert+268, 0x28u, v28)")
    
    print(f"\n🚨 关键发现:")
    print(f"   Type 1和Type 0的验证逻辑几乎相同！")
    print(f"   都会执行密钥哈希验证: memcmp_custom(x0_0, v19, 32)")
    print(f"   您说的'证书等级是1的话就不会检验密匙嘻哈'可能不准确")
    
    return analyze_type1_specific_logic()

def analyze_type1_specific_logic():
    """分析Type 1特有的逻辑"""
    print(f"\n=== Type 1特有逻辑分析 ===")
    
    print(f"\n🔍 Type 1与Type 0的差异:")
    
    print(f"\n1. Payload哈希验证差异:")
    print(f"   Type 1: 直接执行 memcmp_custom(a2, cert+268, 32)")
    print(f"   Type 0: 先检查 if (!memcmp_custom(a2, cert+268, 32))")
    print(f"   - Type 1不检查返回值，Type 0检查返回值")
    print(f"   - 这可能是一个关键差异！")
    
    print(f"\n2. SHA256计算长度差异:")
    print(f"   Type 1: sha256_hash_wrapper(cert+268, 0x48u, v28) // 72字节")
    print(f"   Type 0: sha256_hash_wrapper(cert+268, 0x28u, v28) // 40字节")
    print(f"   - 计算长度不同，可能影响RSA验证")
    
    print(f"\n3. RSA签名位置差异:")
    print(f"   Type 1: v10 = cert+340")
    print(f"   Type 0: v10 = cert+308")
    print(f"   - 签名位置偏移32字节")
    
    print(f"\n💡 潜在的RSA漏洞:")
    
    print(f"\n漏洞1: Type 1的Payload哈希验证绕过")
    print(f"  - Type 1不检查payload哈希验证的返回值")
    print(f"  - 即使payload哈希不匹配，也会继续执行")
    print(f"  - 这可能允许使用错误的payload哈希")
    
    print(f"\n漏洞2: SHA256计算长度操控")
    print(f"  - Type 1计算72字节，Type 0计算40字节")
    print(f"  - 可能通过控制这32字节差异来影响RSA验证")
    
    print(f"\n漏洞3: 签名位置混淆")
    print(f"  - 如果能控制签名读取位置...")
    print(f"  - 可能读取到可控的数据作为签名")
    
    return True

def analyze_uboot3_file():
    """分析uboot3文件"""
    print(f"\n=== uboot3文件分析 ===")
    
    filename = "uboot3"
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return False
    
    print(f"文件大小: {len(data):,} 字节")
    
    # 检查DHTB魔数
    if len(data) >= 4:
        magic = struct.unpack('<I', data[0:4])[0]
        print(f"魔数: 0x{magic:08x} ({'DHTB' if magic == 0x42544844 else '未知'})")
    
    if len(data) >= 52:
        payload_size = struct.unpack('<I', data[48:52])[0]
        print(f"Payload大小: {payload_size:,} 字节")
        
        # 计算证书偏移
        cert_offset_pos = payload_size + 552
        if len(data) >= cert_offset_pos + 8:
            cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
            print(f"证书偏移: 0x{cert_offset:x}")
            
            if len(data) >= cert_offset + 4:
                cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
                print(f"证书类型: {cert_type}")
                
                return analyze_uboot3_vulnerabilities(data, cert_offset, cert_type)
    
    return False

def analyze_uboot3_vulnerabilities(data, cert_offset, cert_type):
    """分析uboot3的漏洞"""
    print(f"\n=== uboot3漏洞分析 ===")
    
    if cert_type == 1:
        print(f"✅ 这是Type 1证书，可以测试Type 1特有的漏洞")
        
        # 测试漏洞1: Payload哈希验证绕过
        print(f"\n🧪 测试漏洞1: Payload哈希验证绕过")
        
        # 读取当前payload哈希
        stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
        print(f"存储的Payload哈希: {stored_payload_hash.hex()}")
        
        # 计算实际payload哈希
        payload_size = struct.unpack('<I', data[48:52])[0]
        payload_data = data[0x200:0x200 + payload_size]
        actual_payload_hash = hashlib.sha256(payload_data).digest()
        print(f"实际Payload哈希: {actual_payload_hash.hex()}")
        
        if stored_payload_hash == actual_payload_hash:
            print(f"  ✅ Payload哈希匹配")
        else:
            print(f"  ❌ Payload哈希不匹配 - 可能存在绕过机会")
        
        # 测试漏洞2: 密钥哈希验证
        print(f"\n🧪 测试漏洞2: 密钥哈希验证")
        
        # 读取期望密钥哈希
        expected_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
        print(f"期望密钥哈希: {expected_key_hash.hex()}")
        
        # 计算实际密钥哈希
        key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
        actual_key_hash = hashlib.sha256(key_data).digest()
        print(f"实际密钥哈希: {actual_key_hash.hex()}")
        
        if expected_key_hash == actual_key_hash:
            print(f"  ✅ 密钥哈希匹配")
        else:
            print(f"  ❌ 密钥哈希不匹配 - 存在绕过机会")
        
        # 测试漏洞3: RSA签名数据分析
        print(f"\n🧪 测试漏洞3: RSA签名数据分析")
        
        # Type 1的RSA签名位置
        rsa_signature = data[cert_offset + 340:cert_offset + 340 + 256]
        print(f"RSA签名位置: cert+340 (0x{cert_offset + 340:x})")
        print(f"RSA签名前16字节: {rsa_signature[:16].hex()}")
        
        # 检查签名是否为全零或特殊模式
        if rsa_signature == b'\x00' * 256:
            print(f"  🚨 RSA签名全为零 - 可能存在绕过机会")
        elif len(set(rsa_signature)) == 1:
            print(f"  🚨 RSA签名为重复字节 - 可能存在绕过机会")
        else:
            print(f"  ✅ RSA签名看起来正常")
        
        return True
    
    else:
        print(f"⚠️  这是Type {cert_type}证书，不是Type 1")
        return False

def create_type1_rsa_bypass(filename):
    """创建Type 1 RSA绕过"""
    print(f"\n=== 创建Type 1 RSA绕过 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    if cert_type != 1:
        print(f"❌ 不是Type 1证书，无法执行此绕过")
        return None
    
    print(f"🎯 利用Type 1的Payload哈希验证绕过漏洞")
    
    # 策略: 由于Type 1不检查payload哈希验证的返回值
    # 我们可以修改payload哈希为任意值，验证仍会继续
    
    # 修改存储的payload哈希为特殊值
    malicious_payload_hash = b"MALICIOUS_PAYLOAD_HASH_BYPASS" + b"\x00" * 3
    data[cert_offset + 268:cert_offset + 268 + 32] = malicious_payload_hash
    print(f"已修改Payload哈希为: {malicious_payload_hash.hex()}")
    
    # 同时修复密钥哈希验证
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    correct_key_hash = hashlib.sha256(key_data).digest()
    data[cert_offset + 0x12c:cert_offset + 0x12c + 32] = correct_key_hash
    print(f"已修复密钥哈希为: {correct_key_hash.hex()}")
    
    # 保存文件
    output_filename = f"{filename}_type1_rsa_bypass"
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"✅ Type 1 RSA绕过文件已生成: {output_filename}")
    
    return output_filename

def main():
    print("RSA漏洞分析")
    print("="*60)
    
    # 分析RSA验证逻辑
    analyze_rsa_verification_logic()
    
    # 分析uboot3文件
    success = analyze_uboot3_file()
    
    if success:
        # 创建Type 1 RSA绕过
        bypass_file = create_type1_rsa_bypass("uboot3")
        
        if bypass_file:
            print(f"\n🎯 Type 1 RSA绕过已生成: {bypass_file}")
    
    print(f"\n{'='*60}")
    print(f"🔍 RSA漏洞分析完成")
    print(f"💡 核心发现: Type 1证书的Payload哈希验证不检查返回值")
    print(f"🎯 绕过原理: 利用Type 1验证逻辑的设计缺陷")
    print(f"⚠️  这可能是一个严重的RSA验证绕过漏洞")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
