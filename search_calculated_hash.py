#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索计算出的密钥哈希在文件中的位置
"""

import struct
import hashlib
import sys

def search_calculated_hash(filename):
    """搜索计算出的密钥哈希在文件中的位置"""
    print(f"=== 搜索计算出的密钥哈希 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 计算密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    print(f"\n计算的密钥哈希: {calculated_key_hash.hex()}")
    
    # 搜索完整的32字节哈希
    print(f"\n=== 搜索完整32字节哈希 ===")
    found_complete = False
    for i in range(len(data) - 31):
        if data[i:i + 32] == calculated_key_hash:
            print(f"✅ 找到完整哈希在偏移: 0x{i:x}")
            print(f"   相对证书偏移: +0x{i - cert_offset:x}")
            found_complete = True
    
    if not found_complete:
        print("❌ 未找到完整的32字节哈希")
    
    # 搜索前16字节
    print(f"\n=== 搜索前16字节 ===")
    front_16 = calculated_key_hash[:16]
    print(f"前16字节: {front_16.hex()}")
    found_front = False
    for i in range(len(data) - 15):
        if data[i:i + 16] == front_16:
            print(f"✅ 找到前16字节在偏移: 0x{i:x}")
            print(f"   相对证书偏移: +0x{i - cert_offset:x}")
            found_front = True
    
    if not found_front:
        print("❌ 未找到前16字节")
    
    # 搜索后16字节
    print(f"\n=== 搜索后16字节 ===")
    back_16 = calculated_key_hash[16:]
    print(f"后16字节: {back_16.hex()}")
    found_back = False
    for i in range(len(data) - 15):
        if data[i:i + 16] == back_16:
            print(f"✅ 找到后16字节在偏移: 0x{i:x}")
            print(f"   相对证书偏移: +0x{i - cert_offset:x}")
            found_back = True
    
    if not found_back:
        print("❌ 未找到后16字节")
    
    # 如果找到了分开的16字节，检查它们是否相邻
    if found_front and found_back:
        print(f"\n=== 检查分开的16字节是否能组成完整哈希 ===")
        # 重新搜索并检查相邻性
        front_positions = []
        back_positions = []
        
        for i in range(len(data) - 15):
            if data[i:i + 16] == front_16:
                front_positions.append(i)
            if data[i:i + 16] == back_16:
                back_positions.append(i)
        
        for front_pos in front_positions:
            for back_pos in back_positions:
                if back_pos == front_pos + 16:
                    print(f"✅ 找到相邻的完整哈希:")
                    print(f"   前16字节位置: 0x{front_pos:x}")
                    print(f"   后16字节位置: 0x{back_pos:x}")
                    print(f"   相对证书偏移: +0x{front_pos - cert_offset:x}")
                    return True
    
    # 特殊检查：也许哈希被存储在其他地方
    print(f"\n=== 特殊搜索：检查是否有部分匹配 ===")
    
    # 检查4字节片段
    for chunk_size in [4, 8, 12]:
        print(f"\n检查{chunk_size}字节片段:")
        for start in range(0, 32, chunk_size):
            chunk = calculated_key_hash[start:start + chunk_size]
            if len(chunk) == chunk_size:
                for i in range(len(data) - chunk_size + 1):
                    if data[i:i + chunk_size] == chunk:
                        print(f"  找到{chunk_size}字节片段[{start}:{start+chunk_size}]在0x{i:x} (cert+0x{i-cert_offset:x})")
                        break
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python search_calculated_hash.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    found = search_calculated_hash(filename)
    
    if found:
        print(f"\n🎉 成功找到计算出的密钥哈希的存储位置！")
    else:
        print(f"\n❌ 计算出的密钥哈希没有存储在文件中")
        print(f"这意味着它可能只用于运行时验证，不需要预存储")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
