#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新正确计算密钥哈希值
基于IDA反汇编的精确分析
"""

import struct
import hashlib
import sys

def analyze_ida_offsets(filename):
    """重新分析IDA反汇编中的偏移"""
    print(f"=== 重新分析IDA反汇编偏移 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"  证书类型: {cert_type}")
    
    print(f"\n=== 重新分析IDA代码 ===")
    print(f"从rsa_signature_verify函数分析:")
    print(f"  cert_address = 证书起始地址")
    print(f"  v8 = cert_address + 4  (第34行)")
    print(f"  sha256_hash_wrapper(v8, 0x108u, v19)  (第37/57行)")
    print(f"  0x108 = {0x108} = 264字节")
    
    # 重新检查证书结构
    print(f"\n=== 证书结构分析 ===")
    print(f"证书起始: 0x{cert_offset:x}")
    print(f"  +0x00: 证书类型 = {cert_type}")
    
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    print(f"  +0x04: 密钥位长度 = {key_bit_len}")
    print(f"  +0x08: e值 = 0x{e_value:x}")
    print(f"  +0x0C: 模数开始 (256字节)")
    print(f"  +0x10C: 模数结束 (cert+4+0x108)")
    
    # 根据证书类型确定哈希存储位置
    if cert_type == 1:
        # Type 1: 哈希在不同位置
        print(f"  +0x10C: payload哈希 (32字节)")
        print(f"  +0x12C: 密钥哈希存储位置 (32字节)")
        hash_storage_offset = 0x12C
    else:
        # Type 0: 哈希在标准位置
        print(f"  +0x10C: payload哈希 (32字节)")
        print(f"  +0x12C: type和version")
        print(f"  +0x134: 签名开始")
        hash_storage_offset = 0x12C  # 需要重新确认
    
    print(f"\n=== 重新计算密钥哈希 ===")
    
    # 方法1: 严格按照IDA代码 - cert+4, 0x108字节
    print(f"方法1: SHA256(cert+4, 0x108字节)")
    key_data_1 = data[cert_offset + 4:cert_offset + 4 + 0x108]
    hash_1 = hashlib.sha256(key_data_1).digest()
    print(f"  数据范围: 0x{cert_offset + 4:x} - 0x{cert_offset + 4 + 0x108:x}")
    print(f"  计算结果: {hash_1.hex()}")
    
    # 方法2: 检查是否应该包含证书类型
    print(f"\n方法2: SHA256(cert+0, 0x10C字节)")
    cert_data_2 = data[cert_offset:cert_offset + 0x10C]
    hash_2 = hashlib.sha256(cert_data_2).digest()
    print(f"  数据范围: 0x{cert_offset:x} - 0x{cert_offset + 0x10C:x}")
    print(f"  计算结果: {hash_2.hex()}")
    
    # 方法3: 只计算密钥部分 (不包含payload哈希)
    print(f"\n方法3: SHA256(cert+4, 0x104字节)")
    key_only_data = data[cert_offset + 4:cert_offset + 4 + 0x104]
    hash_3 = hashlib.sha256(key_only_data).digest()
    print(f"  数据范围: 0x{cert_offset + 4:x} - 0x{cert_offset + 4 + 0x104:x}")
    print(f"  计算结果: {hash_3.hex()}")
    
    # 读取存储的哈希进行比较
    print(f"\n=== 存储的哈希值 ===")
    
    # 检查多个可能的存储位置
    possible_positions = [
        (0x12C, "cert+0x12C"),
        (0x10C, "cert+0x10C (payload哈希位置)"),
        (0x14C, "cert+0x14C"),
        (300, "cert+300 (Type 0标准位置)"),
    ]
    
    stored_hashes = {}
    for offset, desc in possible_positions:
        if cert_offset + offset + 32 <= len(data):
            stored_hash = data[cert_offset + offset:cert_offset + offset + 32]
            stored_hashes[desc] = stored_hash
            print(f"  {desc}: {stored_hash.hex()}")
    
    # 比较所有组合
    print(f"\n=== 匹配检查 ===")
    calculated_hashes = {
        "方法1 (cert+4, 0x108)": hash_1,
        "方法2 (cert+0, 0x10C)": hash_2, 
        "方法3 (cert+4, 0x104)": hash_3,
    }
    
    found_match = False
    for calc_name, calc_hash in calculated_hashes.items():
        for store_name, store_hash in stored_hashes.items():
            if calc_hash == store_hash:
                print(f"✅ 匹配找到!")
                print(f"   计算方法: {calc_name}")
                print(f"   存储位置: {store_name}")
                print(f"   哈希值: {calc_hash.hex()}")
                found_match = True
    
    if not found_match:
        print(f"❌ 没有找到匹配的组合")
        print(f"需要进一步分析IDA代码中的具体参数")
    
    return found_match

def detailed_ida_analysis():
    """详细分析IDA反汇编代码"""
    print(f"\n=== 详细IDA代码分析 ===")
    
    print(f"关键代码行分析:")
    print(f"  第34行: v8 = (int8x16_t *)(cert_address + 4)")
    print(f"  第37行: sha256_hash_wrapper(v8, 0x108u, (__int64)v19)")
    print(f"  第57行: sha256_hash_wrapper(v8, 0x108u, (__int64)v19)")
    print(f"")
    print(f"参数含义:")
    print(f"  v8 = cert_address + 4  -> 从证书偏移4开始")
    print(f"  0x108u = 264          -> 长度264字节")
    print(f"  v19                   -> 输出缓冲区")
    print(f"")
    print(f"比较代码:")
    print(f"  第39行: memcmp_custom(x0_0, (__int64)v19, 32)")
    print(f"  第59行: memcmp_custom(x0_0, (__int64)v19, 32)")
    print(f"")
    print(f"这意味着:")
    print(f"  x0_0 = 传入的期望密钥哈希")
    print(f"  v19  = SHA256(cert+4, 264字节)")
    print(f"  如果 x0_0 == v19，验证通过")

def main():
    if len(sys.argv) != 2:
        print("用法: python correct_key_hash_calculation.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("重新正确计算密钥哈希值")
    print("="*60)
    
    # 详细分析IDA代码
    detailed_ida_analysis()
    
    # 分析偏移和计算
    found_match = analyze_ida_offsets(filename)
    
    print(f"\n{'='*60}")
    if found_match:
        print("🎉 找到正确的计算方法！这确实是官方uboot")
    else:
        print("❓ 需要进一步分析，可能存在理解偏差")
        print("建议:")
        print("  1. 重新检查IDA中的具体偏移")
        print("  2. 确认证书结构的准确性")
        print("  3. 验证SHA256计算的输入数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
