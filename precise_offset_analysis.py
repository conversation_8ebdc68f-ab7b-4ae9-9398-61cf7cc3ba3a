#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确偏移分析工具
重新仔细分析每个偏移计算
"""

import struct
import hashlib
import sys

def analyze_precise_offsets(filename):
    """精确分析每个偏移"""
    print(f"=== 精确偏移分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基础偏移
    payload_size = struct.unpack('<I', data[48:52])[0]
    print(f"Payload大小: 0x{payload_size:x}")
    
    # 根据汇编代码重新分析verify_signature_and_position函数
    print(f"\n=== verify_signature_and_position函数分析 ===")
    print("汇编代码逐行分析:")
    print("0x9f00a004: LDR W1, [X1,#0x30]     ; 读取payload_size")
    print("0x9f00a00c: ADD X1, X1, #0x200     ; X1 = payload_size + 0x200")
    print("0x9f00a010: ADD X3, X19, X1        ; X3 = uboot_base + payload_size + 0x200")
    print("0x9f00a024: LDR W1, [X3,#0x10]     ; 读取 *(uboot_base + payload_size + 0x200 + 0x10)")
    
    # 计算实际偏移
    offset_1 = payload_size + 0x200 + 0x10  # 0x200 + 0x10 = 0x210 = 528
    print(f"\n实际偏移计算:")
    print(f"payload_size + 0x210 = 0x{payload_size:x} + 0x210 = 0x{offset_1:x}")
    
    # 读取这个位置的值
    if offset_1 + 4 <= len(data):
        hash_length = struct.unpack('<I', data[offset_1:offset_1 + 4])[0]
        print(f"读取到的哈希长度: 0x{hash_length:x} ({hash_length} bytes)")
    else:
        print(f"偏移超出文件范围")
        return False
    
    # 分析get_payload_data_address函数
    print(f"\n=== get_payload_data_address函数分析 ===")
    print("汇编代码:")
    print("0x9f009fcc: LDR W1, [X0,#0x30]     ; 读取payload_size")
    print("0x9f009fd0: ADD X1, X0, X1         ; X1 = uboot_base + payload_size")
    print("0x9f009fd4: LDR X1, [X1,#0x218]    ; 读取 *(uboot_base + payload_size + 0x218)")
    print("0x9f009fd8: ADD X0, X0, X1         ; 返回 uboot_base + 读取的值")
    
    # 计算证书偏移位置
    cert_offset_pos = payload_size + 0x218  # 0x218 = 536
    print(f"\n证书偏移位置计算:")
    print(f"payload_size + 0x218 = 0x{payload_size:x} + 0x218 = 0x{cert_offset_pos:x}")
    
    # 但是我们知道实际的证书偏移在payload_size + 552位置
    actual_cert_offset_pos = payload_size + 552
    print(f"实际证书偏移位置: 0x{actual_cert_offset_pos:x}")
    print(f"差异: 0x{actual_cert_offset_pos - cert_offset_pos:x} = {actual_cert_offset_pos - cert_offset_pos} bytes")
    
    # 读取两个位置的值进行比较
    if cert_offset_pos + 8 <= len(data):
        cert_offset_536 = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
        print(f"从+536位置读取: 0x{cert_offset_536:x}")
    
    if actual_cert_offset_pos + 8 <= len(data):
        cert_offset_552 = struct.unpack('<Q', data[actual_cert_offset_pos:actual_cert_offset_pos + 8])[0]
        print(f"从+552位置读取: 0x{cert_offset_552:x}")
    
    # 重新分析哈希计算
    print(f"\n=== 重新分析哈希计算 ===")
    
    # 使用正确的证书偏移
    cert_offset = cert_offset_552
    print(f"使用证书偏移: 0x{cert_offset:x}")
    
    # 重新计算哈希长度位置
    # 根据汇编: X3 = uboot_base + payload_size + 0x200
    # 然后读取 [X3 + 0x10] = [uboot_base + payload_size + 0x210]
    correct_hash_length_pos = payload_size + 0x210
    print(f"正确的哈希长度位置: 0x{correct_hash_length_pos:x}")
    
    if correct_hash_length_pos + 4 <= len(data):
        correct_hash_length = struct.unpack('<I', data[correct_hash_length_pos:correct_hash_length_pos + 4])[0]
        print(f"正确的哈希长度: 0x{correct_hash_length:x} ({correct_hash_length} bytes)")
        
        # 检查这个长度是否合理
        if correct_hash_length > 0 and correct_hash_length < len(data):
            print(f"哈希长度看起来合理")
            
            # 计算从get_payload_data_address返回的地址开始的哈希
            payload_data_address = cert_offset  # get_payload_data_address的返回值
            
            if payload_data_address + correct_hash_length <= len(data):
                calculated_hash = hashlib.sha256(data[payload_data_address:payload_data_address + correct_hash_length]).digest()
                print(f"计算的哈希: {calculated_hash.hex()}")
                
                # 与密钥哈希比较
                key_data_start = cert_offset + 4
                key_data = data[key_data_start:key_data_start + 0x108]
                key_hash = hashlib.sha256(key_data).digest()
                print(f"密钥哈希: {key_hash.hex()}")
                
                if calculated_hash == key_hash:
                    print("🎉 哈希匹配！找到正确的计算方法")
                    return True
                else:
                    print("❌ 哈希不匹配")
        else:
            print(f"哈希长度不合理: {correct_hash_length}")
    
    return False

def analyze_dhtb_structure(filename):
    """分析DHTB结构的每个字段"""
    print(f"\n=== DHTB结构详细分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    # 分析payload_size之后的结构
    print(f"从payload_size(0x{payload_size:x})之后的结构:")
    
    for offset in range(0x200, 0x240, 8):  # 从512到576，每8字节
        pos = payload_size + offset
        if pos + 8 <= len(data):
            value = struct.unpack('<Q', data[pos:pos + 8])[0]
            print(f"  +0x{offset:03x} (0x{pos:x}): 0x{value:016x}")
            
            # 检查是否是合理的偏移值
            if 0x80000 <= value <= 0x90000:
                print(f"    ^ 可能是证书偏移")

def test_bypass_opportunities(filename):
    """测试绕过机会"""
    print(f"\n=== 测试绕过机会 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    print("基于精确偏移分析的绕过策略:")
    
    # 策略1: 修改哈希长度字段
    hash_length_pos = payload_size + 0x210
    print(f"\n策略1: 修改哈希长度字段 (位置: 0x{hash_length_pos:x})")
    print("- 将哈希长度设为0，可能绕过哈希计算")
    print("- 设为特殊值可能导致哈希计算错误")
    
    # 策略2: 修改证书偏移
    cert_offset_pos = payload_size + 0x218
    print(f"\n策略2: 修改证书偏移字段 (位置: 0x{cert_offset_pos:x})")
    print("- 指向构造的假证书")
    print("- 指向文件中的其他数据区域")
    
    # 策略3: 利用偏移差异
    print(f"\n策略3: 利用偏移计算差异")
    print("- 我们发现+536和+552位置都可能被使用")
    print("- 可能存在偏移计算的不一致性")
    
    return True

def main():
    if len(sys.argv) != 2:
        print("用法: python precise_offset_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    # 精确分析偏移
    success = analyze_precise_offsets(filename)
    
    # 分析DHTB结构
    analyze_dhtb_structure(filename)
    
    # 测试绕过机会
    test_bypass_opportunities(filename)
    
    if success:
        print(f"\n✅ 找到正确的偏移计算方法！")
    else:
        print(f"\n⚠️  偏移分析完成，但仍需进一步验证")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
