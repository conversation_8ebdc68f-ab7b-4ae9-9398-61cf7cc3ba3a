#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSA验证漏洞分析
寻找可以绕过验证加载任意uboot的漏洞
"""

import struct
import hashlib
import sys

def analyze_verification_vulnerabilities(filename):
    """分析验证流程中的潜在漏洞"""
    print(f"=== RSA验证漏洞分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取证书信息
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"  证书类型: {cert_type}")
    
    print(f"\n=== 漏洞点分析 ===")
    
    # 漏洞点1: 位置字节检查
    print(f"🔍 漏洞点1: 位置字节检查")
    position_byte_offset = payload_size + 512
    if position_byte_offset < len(data):
        position_byte = data[position_byte_offset]
        print(f"  位置: offset {position_byte_offset} (payload_size + 512)")
        print(f"  当前值: 0x{position_byte:02x}")
        print(f"  要求: 必须为0x00")
        if position_byte != 0:
            print(f"  ⚠️  潜在绕过: 修改此字节为0x00可能绕过检查")
        else:
            print(f"  ✅ 检查通过")
    
    # 漏洞点2: 证书类型检查
    print(f"\n🔍 漏洞点2: 证书类型检查")
    print(f"  当前证书类型: {cert_type}")
    print(f"  IDA代码: if (v6 > 1) return 0")
    print(f"  允许的类型: 0, 1")
    if cert_type > 1:
        print(f"  ❌ 非法证书类型，应该被拒绝")
    else:
        print(f"  ✅ 证书类型合法")
    
    # 漏洞点3: 证书偏移检查
    print(f"\n🔍 漏洞点3: 证书偏移边界检查")
    print(f"  证书偏移: 0x{cert_offset:x}")
    print(f"  文件大小: 0x{len(data):x}")
    min_cert_size = 340 if cert_type == 1 else 300  # Type 1需要更多空间
    if cert_offset + min_cert_size > len(data):
        print(f"  ❌ 证书超出文件边界")
    else:
        print(f"  ✅ 证书在文件范围内")
    
    # 漏洞点4: 整数溢出检查
    print(f"\n🔍 漏洞点4: 整数溢出检查")
    print(f"  payload_size + 512 = 0x{payload_size + 512:x}")
    print(f"  payload_size + 552 = 0x{payload_size + 552:x}")
    if payload_size > 0xFFFFFFFF - 552:
        print(f"  ⚠️  潜在整数溢出")
    else:
        print(f"  ✅ 无整数溢出")
    
    # 漏洞点5: memcmp函数分析
    print(f"\n🔍 漏洞点5: memcmp比较函数")
    print(f"  IDA代码分析:")
    print(f"    Type 0: memcmp_custom(a2, cert+268, 32)")
    print(f"    Type 1: memcmp_custom(a2, cert+268, 32)")
    print(f"    密钥哈希: memcmp_custom(x0_0, v19, 32)")
    print(f"  ⚠️  潜在问题: memcmp_custom实现可能有漏洞")
    
    # 漏洞点6: SHA256计算范围
    print(f"\n🔍 漏洞点6: SHA256计算范围")
    print(f"  IDA代码: sha256_hash_wrapper(cert+4, 0x108u, v19)")
    print(f"  计算范围: cert+4 到 cert+4+0x108 (264字节)")
    print(f"  包含内容: 密钥长度(4) + e值(4) + 模数(256)")
    print(f"  ⚠️  潜在问题: 如果能控制这264字节的内容...")
    
    # 漏洞点7: 证书结构操控
    print(f"\n🔍 漏洞点7: 证书结构操控")
    
    # 检查关键字段
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    
    print(f"  密钥位长度: {key_bit_len}")
    print(f"  e值: 0x{e_value:x}")
    
    # 检查是否可以构造特殊的证书
    print(f"  ⚠️  潜在攻击向量:")
    print(f"    1. 构造特殊的密钥长度值")
    print(f"    2. 使用特殊的e值")
    print(f"    3. 精心设计的模数")
    print(f"    4. 控制SHA256输入使其产生预期哈希")
    
    # 漏洞点8: 哈希碰撞
    print(f"\n🔍 漏洞点8: 哈希碰撞攻击")
    expected_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"  期望密钥哈希: {expected_hash.hex()}")
    print(f"  ⚠️  攻击思路:")
    print(f"    1. 寻找SHA256碰撞")
    print(f"    2. 构造不同的cert+4数据产生相同哈希")
    print(f"    3. 在碰撞数据中嵌入恶意密钥")
    
    return analyze_bypass_strategies(data, cert_offset, cert_type)

def analyze_bypass_strategies(data, cert_offset, cert_type):
    """分析具体的绕过策略"""
    print(f"\n=== 绕过策略分析 ===")
    
    strategies = []
    
    # 策略1: 位置字节绕过
    payload_size = struct.unpack('<I', data[48:52])[0]
    position_byte_offset = payload_size + 512
    if position_byte_offset < len(data):
        position_byte = data[position_byte_offset]
        if position_byte != 0:
            strategies.append({
                'name': '位置字节绕过',
                'description': f'修改offset {position_byte_offset}的字节从0x{position_byte:02x}改为0x00',
                'difficulty': 'Easy',
                'impact': 'High'
            })
    
    # 策略2: 证书类型操控
    if cert_type > 1:
        strategies.append({
            'name': '证书类型绕过',
            'description': f'修改证书类型从{cert_type}改为0或1',
            'difficulty': 'Easy', 
            'impact': 'High'
        })
    
    # 策略3: 哈希预计算攻击
    strategies.append({
        'name': '哈希预计算攻击',
        'description': '预先计算目标哈希，然后构造证书数据匹配该哈希',
        'difficulty': 'Hard',
        'impact': 'Critical'
    })
    
    # 策略4: 整数溢出
    if payload_size > 0x7FFFFFFF:
        strategies.append({
            'name': '整数溢出攻击',
            'description': '利用大的payload_size值造成整数溢出',
            'difficulty': 'Medium',
            'impact': 'High'
        })
    
    # 策略5: 内存布局攻击
    strategies.append({
        'name': '内存布局攻击',
        'description': '精心设计证书布局，使验证函数读取错误的数据',
        'difficulty': 'Hard',
        'impact': 'Critical'
    })
    
    print(f"发现 {len(strategies)} 个潜在绕过策略:")
    for i, strategy in enumerate(strategies, 1):
        print(f"\n策略{i}: {strategy['name']}")
        print(f"  描述: {strategy['description']}")
        print(f"  难度: {strategy['difficulty']}")
        print(f"  影响: {strategy['impact']}")
    
    return strategies

def generate_bypass_payload(filename, strategy_name):
    """生成绕过payload"""
    print(f"\n=== 生成绕过Payload ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    if strategy_name == "位置字节绕过":
        position_byte_offset = payload_size + 512
        if position_byte_offset < len(data):
            original_byte = data[position_byte_offset]
            data[position_byte_offset] = 0x00
            
            output_filename = f"{filename}_position_bypass"
            with open(output_filename, 'wb') as f:
                f.write(data)
            
            print(f"✅ 生成绕过文件: {output_filename}")
            print(f"   修改: offset {position_byte_offset}: 0x{original_byte:02x} -> 0x00")
            return output_filename
    
    return None

def main():
    if len(sys.argv) < 2:
        print("用法: python vulnerability_analysis.py <uboot文件> [生成绕过:策略名]")
        print("策略名: 位置字节绕过")
        return 1
    
    filename = sys.argv[1]
    
    print("RSA验证漏洞分析")
    print("="*60)
    
    # 分析漏洞
    strategies = analyze_verification_vulnerabilities(filename)
    
    # 如果指定了生成绕过
    if len(sys.argv) > 2:
        strategy_name = sys.argv[2]
        bypass_file = generate_bypass_payload(filename, strategy_name)
        if bypass_file:
            print(f"\n🎯 绕过文件已生成: {bypass_file}")
            print(f"⚠️  警告: 仅用于安全研究目的")
    
    print(f"\n{'='*60}")
    print(f"🔍 漏洞分析完成")
    print(f"📊 发现 {len(strategies)} 个潜在攻击向量")
    print(f"⚠️  建议加强验证逻辑以防止绕过")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
