#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的哈希理解脚本
基于所有发现的总结
"""

import struct
import hashlib
import sys

def final_verification_analysis(filename):
    """最终的验证分析"""
    print(f"=== 最终验证逻辑分析 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    print(f"\n=== 验证步骤分析 ===")
    
    # 步骤1: Payload哈希验证 (已确认正确)
    print(f"1. Payload哈希验证:")
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    cert_data_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    payload_pass = payload_hash == cert_data_hash
    
    print(f"   计算: SHA256(data[0x200:0x200 + payload_size])")
    print(f"   比较: cert + 268位置的32字节")
    print(f"   结果: {'✅ 通过' if payload_pass else '❌ 失败'}")
    
    # 步骤2: 密钥哈希验证 (新理解)
    print(f"\n2. 密钥哈希验证:")
    
    # 根据汇编代码，应该计算SHA256(cert+4, 264)
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    # 但比较的目标是cert+0x12c + cert+0x13c的组合
    stored_key_hash_part1 = data[cert_offset + 0x12c:cert_offset + 0x12c + 16]
    stored_key_hash_part2 = data[cert_offset + 0x13c:cert_offset + 0x13c + 16]
    stored_key_hash = stored_key_hash_part1 + stored_key_hash_part2
    
    key_pass = calculated_key_hash == stored_key_hash
    
    print(f"   计算: SHA256(cert + 4, 264字节)")
    print(f"   比较: cert+0x12c (16字节) + cert+0x13c (16字节)")
    print(f"   计算值: {calculated_key_hash.hex()}")
    print(f"   存储值: {stored_key_hash.hex()}")
    print(f"   结果: {'✅ 通过' if key_pass else '❌ 失败'}")
    
    # 步骤3: 位置检查
    print(f"\n3. 位置检查:")
    position_offset = payload_size + 512
    position_pass = False
    if position_offset < len(data):
        position_byte = data[position_offset]
        position_pass = position_byte == 0
        print(f"   检查: data[payload_size + 512] == 0")
        print(f"   位置: 0x{position_offset:x}")
        print(f"   值: 0x{position_byte:02x}")
        print(f"   结果: {'✅ 通过' if position_pass else '❌ 失败'}")
    else:
        print(f"   位置超出文件范围")
    
    # 总结
    print(f"\n=== 验证总结 ===")
    print(f"Payload哈希: {'✅' if payload_pass else '❌'}")
    print(f"密钥哈希: {'✅' if key_pass else '❌'}")
    print(f"位置检查: {'✅' if position_pass else '❌'}")
    
    overall_pass = payload_pass and key_pass and position_pass
    print(f"整体验证: {'✅ 全部通过' if overall_pass else '❌ 存在失败'}")
    
    return overall_pass

def create_bypass_payload_final(filename, output_filename):
    """创建最终的绕过payload"""
    print(f"\n=== 创建绕过payload ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    modifications = []
    
    # 修改1: 确保位置检查通过
    position_offset = payload_size + 512
    if position_offset < len(data):
        if data[position_offset] != 0:
            data[position_offset] = 0
            modifications.append(f"位置检查: 0x{position_offset:x} -> 0x00")
    
    # 修改2: 修改密钥哈希使其匹配
    # 计算当前的密钥哈希
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    calculated_key_hash = hashlib.sha256(key_data).digest()
    
    # 将计算的哈希分成两部分，写入cert+0x12c和cert+0x13c
    new_part1 = calculated_key_hash[:16]
    new_part2 = calculated_key_hash[16:]
    
    data[cert_offset + 0x12c:cert_offset + 0x12c + 16] = new_part1
    data[cert_offset + 0x13c:cert_offset + 0x13c + 16] = new_part2
    
    modifications.append(f"密钥哈希: cert+0x12c和cert+0x13c更新为计算值")
    
    # 修改3: 可选的证书类型降级
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    if cert_type == 1:
        struct.pack_into('<I', data, cert_offset, 0)
        modifications.append(f"证书类型: 1 -> 0 (跳过RSA验证)")
    
    # 保存文件
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"已创建绕过文件: {output_filename}")
    print(f"应用的修改:")
    for mod in modifications:
        print(f"  - {mod}")
    
    return True

def test_bypass_file(filename):
    """测试绕过文件"""
    print(f"\n=== 测试绕过文件 ===")
    
    success = final_verification_analysis(filename)
    
    if success:
        print(f"🎉 绕过成功！所有验证都通过了")
    else:
        print(f"❌ 绕过失败，仍有验证未通过")
    
    return success

def main():
    if len(sys.argv) < 2:
        print("用法: python final_hash_understanding.py <uboot文件> [输出文件]")
        return 1
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "uboot_final_bypass"
    
    print("最终哈希理解和绕过工具")
    print("="*50)
    
    # 1. 分析原始文件
    print("分析原始文件:")
    original_success = final_verification_analysis(input_file)
    
    # 2. 创建绕过文件
    if not original_success:
        create_bypass_payload_final(input_file, output_file)
        
        # 3. 测试绕过文件
        test_bypass_file(output_file)
    else:
        print(f"\n原始文件已经通过所有验证，无需绕过")
    
    print(f"\n{'='*50}")
    print(f"分析完成！")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
