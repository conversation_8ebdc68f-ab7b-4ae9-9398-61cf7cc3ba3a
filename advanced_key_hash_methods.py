#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级密钥哈希计算方法
尝试更多可能的计算方式来验证是否为官方uboot
"""

import struct
import hashlib
import sys

def advanced_key_hash_methods(filename):
    """尝试高级密钥哈希计算方法"""
    print(f"=== 高级密钥哈希计算方法 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    # 目标哈希
    stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"目标密钥哈希: {stored_key_hash.hex()}")
    
    # 获取各种数据
    cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
    key_bit_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
    e_value = struct.unpack('<I', data[cert_offset + 8:cert_offset + 12])[0]
    modulus = data[cert_offset + 12:cert_offset + 12 + 256]
    payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"\n基础信息:")
    print(f"  证书类型: {cert_type}")
    print(f"  密钥位长度: {key_bit_len}")
    print(f"  e值: 0x{e_value:x}")
    
    method_count = 0
    
    # 方法组1: 尝试不同的数据预处理
    print(f"\n=== 数据预处理方法 ===")
    
    # 方法1: 模数的不同字节序
    method_count += 1
    modulus_be = struct.pack('>256s', modulus[::-1])  # 大端序反转
    hash_result = hashlib.sha256(modulus_be).digest()
    print(f"方法{method_count} - 模数大端序反转: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法2: e值和密钥长度的不同组合
    method_count += 1
    e_keylen_combo = struct.pack('>II', e_value, key_bit_len) + modulus
    hash_result = hashlib.sha256(e_keylen_combo).digest()
    print(f"方法{method_count} - e值+密钥长度(大端)+模数: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法3: 尝试包含证书类型的组合
    method_count += 1
    type_key_combo = struct.pack('<I', cert_type) + data[cert_offset + 4:cert_offset + 4 + 264]
    hash_result = hashlib.sha256(type_key_combo).digest()
    print(f"方法{method_count} - 证书类型+密钥数据: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法组2: 尝试特殊的哈希链
    print(f"\n=== 哈希链方法 ===")
    
    # 方法4: 模数哈希 + payload哈希
    method_count += 1
    mod_hash = hashlib.sha256(modulus).digest()
    chain_hash = hashlib.sha256(mod_hash + payload_hash).digest()
    print(f"方法{method_count} - SHA256(模数哈希+payload哈希): {'✅' if chain_hash == stored_key_hash else '❌'}")
    if chain_hash == stored_key_hash:
        return True
    
    # 方法5: payload哈希 + 模数哈希
    method_count += 1
    chain_hash2 = hashlib.sha256(payload_hash + mod_hash).digest()
    print(f"方法{method_count} - SHA256(payload哈希+模数哈希): {'✅' if chain_hash2 == stored_key_hash else '❌'}")
    if chain_hash2 == stored_key_hash:
        return True
    
    # 方法6: 三重哈希
    method_count += 1
    triple_hash = hashlib.sha256(hashlib.sha256(hashlib.sha256(modulus).digest()).digest()).digest()
    print(f"方法{method_count} - SHA256³(模数): {'✅' if triple_hash == stored_key_hash else '❌'}")
    if triple_hash == stored_key_hash:
        return True
    
    # 方法组3: 尝试包含外部数据
    print(f"\n=== 外部数据方法 ===")
    
    # 方法7: 包含payload大小
    method_count += 1
    size_key_combo = struct.pack('<I', payload_size) + data[cert_offset + 4:cert_offset + 4 + 264]
    hash_result = hashlib.sha256(size_key_combo).digest()
    print(f"方法{method_count} - payload大小+密钥数据: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法8: 包含证书偏移
    method_count += 1
    offset_key_combo = struct.pack('<Q', cert_offset) + data[cert_offset + 4:cert_offset + 4 + 264]
    hash_result = hashlib.sha256(offset_key_combo).digest()
    print(f"方法{method_count} - 证书偏移+密钥数据: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法9: 包含DHTB magic
    method_count += 1
    magic = struct.unpack('<I', data[0:4])[0]
    magic_key_combo = struct.pack('<I', magic) + data[cert_offset + 4:cert_offset + 4 + 264]
    hash_result = hashlib.sha256(magic_key_combo).digest()
    print(f"方法{method_count} - DHTB magic+密钥数据: {'✅' if hash_result == stored_key_hash else '❌'}")
    if hash_result == stored_key_hash:
        return True
    
    # 方法组4: 尝试分段计算
    print(f"\n=== 分段计算方法 ===")
    
    # 方法10: 分别计算e值和模数，然后组合
    method_count += 1
    e_hash = hashlib.sha256(struct.pack('<I', e_value)).digest()
    mod_hash = hashlib.sha256(modulus).digest()
    combined_hash = hashlib.sha256(e_hash + mod_hash).digest()
    print(f"方法{method_count} - SHA256(e值哈希+模数哈希): {'✅' if combined_hash == stored_key_hash else '❌'}")
    if combined_hash == stored_key_hash:
        return True
    
    # 方法11: 密钥长度哈希 + 模数哈希
    method_count += 1
    keylen_hash = hashlib.sha256(struct.pack('<I', key_bit_len)).digest()
    combined_hash2 = hashlib.sha256(keylen_hash + mod_hash).digest()
    print(f"方法{method_count} - SHA256(密钥长度哈希+模数哈希): {'✅' if combined_hash2 == stored_key_hash else '❌'}")
    if combined_hash2 == stored_key_hash:
        return True
    
    # 方法12: 尝试XOR操作
    method_count += 1
    key_data = data[cert_offset + 4:cert_offset + 4 + 264]
    key_hash = hashlib.sha256(key_data).digest()
    xor_result = bytes(a ^ b for a, b in zip(key_hash, payload_hash))
    print(f"方法{method_count} - 密钥哈希 XOR payload哈希: {'✅' if xor_result == stored_key_hash else '❌'}")
    if xor_result == stored_key_hash:
        return True
    
    # 方法组5: 尝试特殊的数据范围
    print(f"\n=== 特殊数据范围方法 ===")
    
    # 方法13: 尝试不同的起始位置
    for start_offset in [0, 1, 2, 3, 5, 6, 7]:
        method_count += 1
        test_data = data[cert_offset + start_offset:cert_offset + start_offset + 264]
        if len(test_data) == 264:
            hash_result = hashlib.sha256(test_data).digest()
            match = hash_result == stored_key_hash
            print(f"方法{method_count} - cert+{start_offset}, 264字节: {'✅' if match else '❌'}")
            if match:
                return True
    
    # 方法14: 尝试不同的长度
    for length in [261, 262, 263, 265, 266, 267]:
        method_count += 1
        test_data = data[cert_offset + 4:cert_offset + 4 + length]
        if cert_offset + 4 + length <= len(data):
            hash_result = hashlib.sha256(test_data).digest()
            match = hash_result == stored_key_hash
            print(f"方法{method_count} - cert+4, {length}字节: {'✅' if match else '❌'}")
            if match:
                return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python advanced_key_hash_methods.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("高级密钥哈希计算方法测试")
    print("="*60)
    
    is_official = advanced_key_hash_methods(filename)
    
    print(f"\n{'='*60}")
    if is_official:
        print("🎉 找到匹配的计算方法！")
        print("✅ 结论: 这是官方uboot，密钥哈希验证通过")
    else:
        print("❌ 所有高级方法都不匹配")
        print("❓ 结论: 这可能不是官方uboot，或使用了未知算法")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
