#!/usr/bin/env python3
"""Secure Boot绕过综合测试"""
import struct
import hashlib

def test_secure_boot_bypass():
    """测试secure boot绕过"""
    files_to_test = [
        "uboot3_secure_boot_bypass",
        "uboot3_fixed_type1_exploit"
    ]
    
    for filename in files_to_test:
        try:
            print(f"\n测试文件: {filename}")
            with open(filename, 'rb') as f:
                data = f.read()
            
            payload_size = struct.unpack('<I', data[48:52])[0]
            cert_offset_pos = payload_size + 552
            cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
            cert_type = struct.unpack('<I', data[cert_offset:cert_offset + 4])[0]
            
            print(f"  证书类型: {cert_type}")
            
            if cert_type == 1:
                # 检查payload
                payload_data = data[0x200:0x200 + payload_size]
                if b"SECURE_BOOT_BYPASS" in payload_data or b"MALICIOUS" in payload_data:
                    print(f"  ✅ 检测到恶意payload")
                
                # 检查哈希
                stored_payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
                actual_payload_hash = hashlib.sha256(payload_data).digest()
                
                if stored_payload_hash != actual_payload_hash:
                    print(f"  ✅ Payload哈希不匹配 - Type 1绕过条件满足")
                
                # 检查RSA密钥区域
                rsa_area = data[cert_offset + 4:cert_offset + 4 + 100]
                if b"\x01\x00\x00\x00" in rsa_area:
                    print(f"  🚨 可能检测到弱RSA密钥 (e=1)")
                
                print(f"  💡 预期: 由于Type 1漏洞，验证应该通过")
            else:
                print(f"  ⚠️  不是Type 1证书")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")

if __name__ == "__main__":
    test_secure_boot_bypass()
