#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的偏移分析
基于汇编代码重新理解偏移计算
"""

import struct
import hashlib
import sys

def analyze_correct_offsets(filename):
    """基于汇编代码分析正确的偏移"""
    print(f"=== 正确偏移分析: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 基础信息
    payload_size = struct.unpack('<I', data[48:52])[0]
    print(f"Payload大小: 0x{payload_size:x}")
    
    # 根据汇编代码重新分析
    print(f"\n=== 汇编代码逐步分析 ===")
    
    # 步骤1: 0x9f00a004 - LDR W1, [X1,#0x30]
    print(f"1. LDR W1, [X1,#0x30] - 读取payload_size")
    print(f"   payload_size = 0x{payload_size:x}")
    
    # 步骤2: 0x9f00a00c - ADD X1, X1, #0x200  
    offset_1 = payload_size + 0x200
    print(f"2. ADD X1, X1, #0x200")
    print(f"   X1 = payload_size + 0x200 = 0x{offset_1:x}")
    
    # 步骤3: 0x9f00a010 - ADD X3, X19, X1
    print(f"3. ADD X3, X19, X1")
    print(f"   X3 = uboot_base + X1 = uboot_base + 0x{offset_1:x}")
    
    # 步骤4: 0x9f00a024 - LDR W1, [X3,#0x10]
    hash_length_offset = offset_1 + 0x10  # 0x200 + 0x10 = 0x210
    print(f"4. LDR W1, [X3,#0x10]")
    print(f"   读取位置: uboot_base + 0x{hash_length_offset:x}")
    print(f"   实际文件偏移: 0x{hash_length_offset:x}")
    
    # 读取哈希长度
    if hash_length_offset + 4 <= len(data):
        hash_length = struct.unpack('<I', data[hash_length_offset:hash_length_offset + 4])[0]
        print(f"   读取的哈希长度: 0x{hash_length:x} ({hash_length} bytes)")
        
        # 检查这个长度是否合理
        if 0 < hash_length < len(data):
            print(f"   ✅ 哈希长度合理")
        else:
            print(f"   ❌ 哈希长度不合理")
            return False
    else:
        print(f"   ❌ 偏移超出文件范围")
        return False
    
    # 现在分析get_payload_data_address函数
    print(f"\n=== get_payload_data_address函数分析 ===")
    
    # 根据伪C代码: return a1 + *(_QWORD *)(a1 + *(unsigned int *)(a1 + 48) + 536);
    # 这意味着: return uboot_base + *(uboot_base + payload_size + 536)
    
    cert_offset_pos = payload_size + 536  # 不是552！
    print(f"证书偏移位置: payload_size + 536 = 0x{cert_offset_pos:x}")
    
    if cert_offset_pos + 8 <= len(data):
        cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
        print(f"证书偏移值: 0x{cert_offset:x}")
        
        # 计算payload数据地址
        payload_data_address = cert_offset  # get_payload_data_address的返回值
        print(f"Payload数据地址: 0x{payload_data_address:x}")
        
        # 现在用正确的参数计算哈希
        if payload_data_address + hash_length <= len(data):
            print(f"\n=== 正确的哈希计算 ===")
            print(f"数据起始: 0x{payload_data_address:x}")
            print(f"数据长度: {hash_length} bytes")
            
            hash_data = data[payload_data_address:payload_data_address + hash_length]
            calculated_hash = hashlib.sha256(hash_data).digest()
            
            print(f"计算的哈希: {calculated_hash.hex()}")
            
            # 与目标哈希比较
            target_hash = bytes.fromhex("2ffda7503f684a313b81395f6da5cc593a1895cfe8658406c7d159752c47575e")
            print(f"目标哈希:   {target_hash.hex()}")
            print(f"匹配:       {'YES' if calculated_hash == target_hash else 'NO'}")
            
            if calculated_hash == target_hash:
                print(f"🎉 找到正确的密钥哈希计算方法！")
                return True
        else:
            print(f"❌ 哈希计算范围超出文件")
    else:
        print(f"❌ 证书偏移位置超出文件")
    
    return False

def verify_all_calculations(filename):
    """验证所有计算是否正确"""
    print(f"\n=== 验证所有计算 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    
    # 使用正确的偏移
    cert_offset_pos = payload_size + 536  # 正确的偏移
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    hash_length_offset = payload_size + 0x210
    hash_length = struct.unpack('<I', data[hash_length_offset:hash_length_offset + 4])[0]
    
    print(f"使用的参数:")
    print(f"  证书偏移位置: 0x{cert_offset_pos:x} (payload_size + 536)")
    print(f"  证书偏移值: 0x{cert_offset:x}")
    print(f"  哈希长度位置: 0x{hash_length_offset:x} (payload_size + 0x210)")
    print(f"  哈希长度: {hash_length}")
    
    # 计算密钥哈希
    hash_data = data[cert_offset:cert_offset + hash_length]
    calculated_hash = hashlib.sha256(hash_data).digest()
    
    # 获取存储的哈希
    stored_hash_pos = cert_offset + 300
    stored_hash = data[stored_hash_pos:stored_hash_pos + 32]
    
    print(f"\n哈希计算结果:")
    print(f"  计算的哈希: {calculated_hash.hex()}")
    print(f"  存储的哈希: {stored_hash.hex()}")
    print(f"  匹配: {'YES' if calculated_hash == stored_hash else 'NO'}")
    
    # 同时验证payload哈希
    payload_hash = hashlib.sha256(data[0x200:0x200 + payload_size]).digest()
    cert_data_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    
    print(f"\n数据哈希验证:")
    print(f"  Payload哈希: {payload_hash.hex()}")
    print(f"  证书数据哈希: {cert_data_hash.hex()}")
    print(f"  匹配: {'YES' if payload_hash == cert_data_hash else 'NO'}")
    
    return calculated_hash == stored_hash

def main():
    if len(sys.argv) != 2:
        print("用法: python correct_offset_analysis.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("正确偏移分析工具")
    print("="*50)
    
    # 分析正确的偏移
    success1 = analyze_correct_offsets(filename)
    
    # 验证所有计算
    success2 = verify_all_calculations(filename)
    
    print(f"\n{'='*50}")
    if success1 or success2:
        print("✅ 找到正确的偏移计算方法！")
        print("密钥哈希验证应该能够通过")
    else:
        print("❌ 仍需要进一步分析")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
