#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新理解密钥哈希计算
如果计算出的哈希匹配存储的哈希，说明是官方uboot
"""

import struct
import hashlib
import sys

def rethink_key_hash_calculation(filename):
    """重新理解密钥哈希计算逻辑"""
    print(f"=== 重新理解密钥哈希计算 ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    payload_size = struct.unpack('<I', data[48:52])[0]
    cert_offset_pos = payload_size + 552
    cert_offset = struct.unpack('<Q', data[cert_offset_pos:cert_offset_pos + 8])[0]
    
    print(f"基础参数:")
    print(f"  Payload大小: 0x{payload_size:x}")
    print(f"  证书偏移: 0x{cert_offset:x}")
    
    # 读取存储的密钥哈希 (cert+0x12c, 32字节)
    stored_key_hash = data[cert_offset + 0x12c:cert_offset + 0x12c + 32]
    print(f"\n存储的密钥哈希 (cert+0x12c):")
    print(f"  {stored_key_hash.hex()}")
    
    # 重新思考：也许我需要计算的不是cert+4开始的264字节
    # 让我尝试不同的计算方法
    
    print(f"\n=== 尝试不同的计算方法 ===")
    
    # 方法1: 原来的方法 - SHA256(cert+4, 264字节)
    key_data_original = data[cert_offset + 4:cert_offset + 4 + 264]
    hash1 = hashlib.sha256(key_data_original).digest()
    print(f"方法1 - SHA256(cert+4, 264字节):")
    print(f"  {hash1.hex()}")
    print(f"  匹配: {'✅' if hash1 == stored_key_hash else '❌'}")
    
    # 方法2: SHA256(cert+0, 268字节) - 包含证书类型
    cert_to_hash = data[cert_offset:cert_offset + 268]
    hash2 = hashlib.sha256(cert_to_hash).digest()
    print(f"\n方法2 - SHA256(cert+0, 268字节):")
    print(f"  {hash2.hex()}")
    print(f"  匹配: {'✅' if hash2 == stored_key_hash else '❌'}")
    
    # 方法3: SHA256(cert+0, 300字节) - 到密钥哈希位置
    cert_to_key = data[cert_offset:cert_offset + 300]
    hash3 = hashlib.sha256(cert_to_key).digest()
    print(f"\n方法3 - SHA256(cert+0, 300字节):")
    print(f"  {hash3.hex()}")
    print(f"  匹配: {'✅' if hash3 == stored_key_hash else '❌'}")
    
    # 方法4: 只计算模数 SHA256(cert+12, 256字节)
    modulus_only = data[cert_offset + 12:cert_offset + 12 + 256]
    hash4 = hashlib.sha256(modulus_only).digest()
    print(f"\n方法4 - SHA256(模数, 256字节):")
    print(f"  {hash4.hex()}")
    print(f"  匹配: {'✅' if hash4 == stored_key_hash else '❌'}")
    
    # 方法5: 计算公钥部分 SHA256(cert+4, 260字节) - 不包含payload哈希
    pubkey_data = data[cert_offset + 4:cert_offset + 4 + 260]
    hash5 = hashlib.sha256(pubkey_data).digest()
    print(f"\n方法5 - SHA256(公钥部分, 260字节):")
    print(f"  {hash5.hex()}")
    print(f"  匹配: {'✅' if hash5 == stored_key_hash else '❌'}")
    
    # 方法6: 计算密钥+e值 SHA256(cert+4, 8字节) + SHA256(模数)
    key_header = data[cert_offset + 4:cert_offset + 12]
    modulus = data[cert_offset + 12:cert_offset + 12 + 256]
    combined_hash = hashlib.sha256(key_header + modulus).digest()
    hash6 = hashlib.sha256(combined_hash).digest()
    print(f"\n方法6 - SHA256(SHA256(密钥头+模数)):")
    print(f"  {hash6.hex()}")
    print(f"  匹配: {'✅' if hash6 == stored_key_hash else '❌'}")
    
    # 方法7: 尝试包含payload哈希的计算
    payload_hash = data[cert_offset + 268:cert_offset + 268 + 32]
    key_plus_payload = key_data_original + payload_hash
    hash7 = hashlib.sha256(key_plus_payload).digest()
    print(f"\n方法7 - SHA256(密钥数据+payload哈希):")
    print(f"  {hash7.hex()}")
    print(f"  匹配: {'✅' if hash7 == stored_key_hash else '❌'}")
    
    # 方法8: 尝试反向 - payload哈希+密钥数据
    payload_plus_key = payload_hash + key_data_original
    hash8 = hashlib.sha256(payload_plus_key).digest()
    print(f"\n方法8 - SHA256(payload哈希+密钥数据):")
    print(f"  {hash8.hex()}")
    print(f"  匹配: {'✅' if hash8 == stored_key_hash else '❌'}")
    
    # 方法9: 尝试双重哈希
    double_hash = hashlib.sha256(hashlib.sha256(key_data_original).digest()).digest()
    print(f"\n方法9 - SHA256(SHA256(密钥数据)):")
    print(f"  {double_hash.hex()}")
    print(f"  匹配: {'✅' if double_hash == stored_key_hash else '❌'}")
    
    # 方法10: 尝试包含文件头信息
    file_header = data[0:52]  # DHTB头
    header_plus_key = file_header + key_data_original
    hash10 = hashlib.sha256(header_plus_key).digest()
    print(f"\n方法10 - SHA256(文件头+密钥数据):")
    print(f"  {hash10.hex()}")
    print(f"  匹配: {'✅' if hash10 == stored_key_hash else '❌'}")
    
    # 检查是否有任何匹配
    all_hashes = [hash1, hash2, hash3, hash4, hash5, hash6, hash7, hash8, double_hash, hash10]
    matches = [i+1 for i, h in enumerate(all_hashes) if h == stored_key_hash]
    
    print(f"\n=== 总结 ===")
    if matches:
        print(f"🎉 找到匹配的计算方法: 方法{matches}")
        print(f"这说明这是官方uboot，密钥哈希验证通过！")
        return True
    else:
        print(f"❌ 所有方法都不匹配")
        print(f"这可能意味着:")
        print(f"  1. 这不是官方uboot")
        print(f"  2. 使用了我们未知的计算方法")
        print(f"  3. 密钥哈希是预设的固定值")
        return False

def main():
    if len(sys.argv) != 2:
        print("用法: python rethink_key_hash_calculation.py <uboot文件>")
        return 1
    
    filename = sys.argv[1]
    
    print("重新理解密钥哈希计算")
    print("="*60)
    
    is_official = rethink_key_hash_calculation(filename)
    
    print(f"\n{'='*60}")
    if is_official:
        print("✅ 结论: 这是官方uboot")
    else:
        print("❓ 结论: 需要进一步分析")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
